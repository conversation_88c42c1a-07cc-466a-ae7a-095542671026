package com.morningstar.martkafkaconsumer.util;

import static org.junit.Assert.assertArrayEquals;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

import io.lettuce.core.cluster.ClusterClientOptions;
import io.lettuce.core.resource.ClientResources;
import io.lettuce.core.resource.DefaultClientResources;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class RedisUtilTest {

    private ClientResources clientResources;
    private ClusterClientOptions clientOptions;
    private long bufferMillis;

    @Before
    public void setUp() {
        clientResources = DefaultClientResources.create();
        clientOptions = ClusterClientOptions.builder().build();
        bufferMillis = getBufferMillisFromRedisUtil();
    }

    @After
    public void tearDown() {
        if (clientResources != null) {
            clientResources.shutdown();
        }
    }

    @Test
    public void testCreateLettuceConnectionFactories() {
        String host = "localhost";
        int port = 6379;
        String clientName = "test-client";
        int totalFactories = 3;

        List<LettuceConnectionFactory> factories = RedisUtil.createLettuceConnectionFactories(
                host, port, clientName, totalFactories, clientResources, clientOptions);

        assertNotNull("Factory list should not be null", factories);
        assertEquals("Should create exact number of factories", totalFactories, factories.size());

        // Verify each factory is properly initialized
        for (LettuceConnectionFactory factory : factories) {
            assertNotNull("Factory should not be null", factory);
        }

        // Clean up factories
        factories.forEach(factory -> {
            try {
                factory.destroy();
            } catch (Exception e) {
                // Ignore cleanup errors in tests
            }
        });
    }

    @Test
    public void testCreateLettuceConnectionFactoriesReadOnly() {
        String host = "localhost";
        int port = 6379;
        String clientName = "test-client";
        int totalFactories = 2;

        List<LettuceConnectionFactory> factories = RedisUtil.createLettuceConnectionFactoriesReadOnly(
                host, port, clientName, totalFactories, clientResources, clientOptions);

        assertNotNull("Factory list should not be null", factories);
        assertEquals("Should create exact number of factories", totalFactories, factories.size());

        // Verify each factory is properly initialized
        for (LettuceConnectionFactory factory : factories) {
            assertNotNull("Factory should not be null", factory);
        }

        // Clean up factories
        factories.forEach(factory -> {
            try {
                factory.destroy();
            } catch (Exception e) {
                // Ignore cleanup errors in tests
            }
        });
    }

    @Test
    public void testCreateSingleLettuceConnectionFactory() {
        String host = "localhost";
        int port = 6379;
        String clientName = "single-test-client";
        boolean isReadOnly = false;

        LettuceConnectionFactory factory = RedisUtil.createLettuceConnectionFactory(
                host, port, clientName, isReadOnly, clientResources, clientOptions);

        assertNotNull("Factory should not be null", factory);

        // Clean up factory
        try {
            factory.destroy();
        } catch (Exception e) {
            // Ignore cleanup errors in tests
        }
    }

    @Test
    public void testCreateSingleLettuceConnectionFactoryReadOnly() {
        String host = "localhost";
        int port = 6379;
        String clientName = "single-readonly-test-client";
        boolean isReadOnly = true;

        LettuceConnectionFactory factory = RedisUtil.createLettuceConnectionFactory(
                host, port, clientName, isReadOnly, clientResources, clientOptions);

        assertNotNull("Factory should not be null", factory);

        // Clean up factory
        try {
            factory.destroy();
        } catch (Exception e) {
            // Ignore cleanup errors in tests
        }
    }

    @Test
    public void testClientNamingConvention() {
        String host = "localhost";
        int port = 6379;
        String clientName = "test-client";
        int totalFactories = 2;

        // Test regular factories naming
        List<LettuceConnectionFactory> regularFactories = RedisUtil.createLettuceConnectionFactories(
                host, port, clientName, totalFactories, clientResources, clientOptions);

        // Test readonly factories naming
        List<LettuceConnectionFactory> readOnlyFactories = RedisUtil.createLettuceConnectionFactoriesReadOnly(
                host, port, clientName, totalFactories, clientResources, clientOptions);

        assertNotNull("Regular factory list should not be null", regularFactories);
        assertNotNull("ReadOnly factory list should not be null", readOnlyFactories);
        assertEquals("Should create exact number of regular factories", totalFactories, regularFactories.size());
        assertEquals("Should create exact number of readonly factories", totalFactories, readOnlyFactories.size());

        // Clean up all factories
        regularFactories.forEach(factory -> {
            try {
                factory.destroy();
            } catch (Exception e) {
                // Ignore cleanup errors in tests
            }
        });

        readOnlyFactories.forEach(factory -> {
            try {
                factory.destroy();
            } catch (Exception e) {
                // Ignore cleanup errors in tests
            }
        });
    }

    @Test
    public void testAddExpireFieldWithHours() {
        Map<byte[], byte[]> fieldMap = new HashMap<>();
        int expireHours = 2;

        long beforeTime = System.currentTimeMillis();
        RedisUtil.addExpireField(fieldMap, expireHours, RedisUtil.TimeUnit.HOURS);
        long afterTime = System.currentTimeMillis();

        // Verify the expire field was added
        assertEquals("Field map should contain exactly one entry", 1, fieldMap.size());

        // Get the first (and only) entry from the map
        Map.Entry<byte[], byte[]> entry = fieldMap.entrySet().iterator().next();
        byte[] keyBytes = entry.getKey();
        byte[] valueBytes = entry.getValue();

        // Verify the key is "expire"
        assertEquals("Key should be 'expire'", "expire", new String(keyBytes));
        assertNotNull("Expire value should not be null", valueBytes);

        // Convert back to verify the time calculation
        long expireTime = NumberUtil.bytesToLong(valueBytes);

        // Expected time should be current time + 2 hours + buffer
        long expectedMinTime = beforeTime + (2 * 3_600_000L) + bufferMillis;
        long expectedMaxTime = afterTime + (2 * 3_600_000L) + bufferMillis;

        assertTrue("Expire time should be within expected range",
                expireTime >= expectedMinTime && expireTime <= expectedMaxTime);
    }

    @Test
    public void testAddExpireFieldWithMinutes() {
        Map<byte[], byte[]> fieldMap = new HashMap<>();
        int expireMinutes = 30;

        long beforeTime = System.currentTimeMillis();
        RedisUtil.addExpireField(fieldMap, expireMinutes, RedisUtil.TimeUnit.MINUTES);
        long afterTime = System.currentTimeMillis();

        // Verify the expire field was added
        assertEquals("Field map should contain exactly one entry", 1, fieldMap.size());

        // Get the first (and only) entry from the map
        Map.Entry<byte[], byte[]> entry = fieldMap.entrySet().iterator().next();
        byte[] valueBytes = entry.getValue();

        // Convert back to verify the time calculation
        long expireTime = NumberUtil.bytesToLong(valueBytes);

        // Expected time should be current time + 30 minutes + buffer
        long expectedMinTime = beforeTime + (30 * 60_000L) + bufferMillis;
        long expectedMaxTime = afterTime + (30 * 60_000L) + bufferMillis;

        assertTrue("Expire time should be within expected range",
                expireTime >= expectedMinTime && expireTime <= expectedMaxTime);
    }

    @Test
    public void testAddExpireFieldWithSeconds() {
        Map<byte[], byte[]> fieldMap = new HashMap<>();
        int expireSeconds = 300; // 5 minutes

        long beforeTime = System.currentTimeMillis();
        RedisUtil.addExpireField(fieldMap, expireSeconds, RedisUtil.TimeUnit.SECONDS);
        long afterTime = System.currentTimeMillis();

        // Verify the expire field was added
        assertEquals("Field map should contain exactly one entry", 1, fieldMap.size());

        // Get the first (and only) entry from the map
        Map.Entry<byte[], byte[]> entry = fieldMap.entrySet().iterator().next();
        byte[] valueBytes = entry.getValue();

        // Convert back to verify the time calculation
        long expireTime = NumberUtil.bytesToLong(valueBytes);

        // Expected time should be current time + 300 seconds + buffer
        long expectedMinTime = beforeTime + (300 * 1000L) + bufferMillis;
        long expectedMaxTime = afterTime + (300 * 1000L) + bufferMillis;

        assertTrue("Expire time should be within expected range",
                expireTime >= expectedMinTime && expireTime <= expectedMaxTime);
    }

    @Test
    public void testAddExpireFieldOverwritesExistingValue() {
        Map<byte[], byte[]> fieldMap = new HashMap<>();

        // Add initial expire field
        RedisUtil.addExpireField(fieldMap, 1, RedisUtil.TimeUnit.HOURS);
        byte[] firstExpireValue = fieldMap.entrySet().iterator().next().getValue();

        // Add another expire field (should overwrite)
        RedisUtil.addExpireField(fieldMap, 2, RedisUtil.TimeUnit.HOURS);
        byte[] secondExpireValue = fieldMap.entrySet().iterator().next().getValue();

        // Verify the value was overwritten
        assertEquals("Field map should still contain only one entry", 1, fieldMap.size());
        assertTrue("Second expire value should be different from first",
                !java.util.Arrays.equals(firstExpireValue, secondExpireValue));

        // Verify second value is larger (later time)
        long firstTime = NumberUtil.bytesToLong(firstExpireValue);
        long secondTime = NumberUtil.bytesToLong(secondExpireValue);
        assertTrue("Second expire time should be later than first", secondTime > firstTime);
    }


    @Test
    public void testTimeUnitConversions() {
        // Test that different time units produce equivalent results
        Map<byte[], byte[]> hoursMap = new HashMap<>();
        Map<byte[], byte[]> minutesMap = new HashMap<>();
        Map<byte[], byte[]> secondsMap = new HashMap<>();

        // 1 hour = 60 minutes = 3600 seconds
        RedisUtil.addExpireField(hoursMap, 1, RedisUtil.TimeUnit.HOURS);
        RedisUtil.addExpireField(minutesMap, 60, RedisUtil.TimeUnit.MINUTES);
        RedisUtil.addExpireField(secondsMap, 3600, RedisUtil.TimeUnit.SECONDS);

        long hoursTime = NumberUtil.bytesToLong(hoursMap.entrySet().iterator().next().getValue());
        long minutesTime = NumberUtil.bytesToLong(minutesMap.entrySet().iterator().next().getValue());
        long secondsTime = NumberUtil.bytesToLong(secondsMap.entrySet().iterator().next().getValue());

        // Allow for small time differences due to execution time
        long tolerance = 100L; // 100ms tolerance

        assertTrue("Hours and minutes should produce similar expire times",
                Math.abs(hoursTime - minutesTime) <= tolerance);
        assertTrue("Hours and seconds should produce similar expire times",
                Math.abs(hoursTime - secondsTime) <= tolerance);
        assertTrue("Minutes and seconds should produce similar expire times",
                Math.abs(minutesTime - secondsTime) <= tolerance);
    }

    private long getBufferMillisFromRedisUtil() {
        try {
            java.lang.reflect.Field bufferField = RedisUtil.class.getDeclaredField("BUFFER_MILLIS");
            bufferField.setAccessible(true);
            return bufferField.getLong(null);
        } catch (Exception e) {
            throw new RuntimeException("Failed to access BUFFER_MILLIS constant", e);
        }
    }

    @Test
    public void testGetExpireKey() {
        byte[] expireKey = RedisUtil.getExpireKey();
        assertNotNull(expireKey);
        assertArrayEquals("expire".getBytes(StandardCharsets.UTF_8), expireKey);
    }
}