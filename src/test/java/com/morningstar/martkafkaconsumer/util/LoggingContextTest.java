package com.morningstar.martkafkaconsumer.util;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.slf4j.MDC;

import static org.junit.jupiter.api.Assertions.*;

class LoggingContextTest {

    @BeforeEach
    void setUp() {
        MDC.clear();
    }

    @AfterEach
    void tearDown() {
        MDC.clear();
    }

    @Test
    @DisplayName("setTraceId_WithValidTraceId_ShouldSetMDCValue")
    void setTraceId_WithValidTraceId_ShouldSetMDCValue() {
        String traceId = "test-trace-id-123";

        LoggingContext.setTraceId(traceId);

        assertEquals(traceId, LoggingContext.getTraceId());
        assertEquals(traceId, MDC.get("traceId"));
    }

    @Test
    @DisplayName("setTraceId_WithNullTraceId_ShouldNotSetMDCValue")
    void setTraceId_WithNullTraceId_ShouldNotSetMDCValue() {
        LoggingContext.setTraceId(null);

        assertNull(LoggingContext.getTraceId());
        assertNull(MDC.get("traceId"));
    }


    @Test
    @DisplayName("clearTraceId_ShouldRemoveTraceIdFromMDC")
    void clearTraceId_ShouldRemoveTraceIdFromMDC() {
        LoggingContext.setTraceId("test-trace-id");

        LoggingContext.clearTraceId();

        assertNull(LoggingContext.getTraceId());
        assertNull(MDC.get("traceId"));
    }

    @Test
    @DisplayName("clearAll_ShouldRemoveAllMDCValues")
    void clearAll_ShouldRemoveAllMDCValues() {

        LoggingContext.setTraceId("test-trace-id");
        MDC.put("otherKey", "otherValue");

        LoggingContext.clearAll();

        assertNull(LoggingContext.getTraceId());
        assertNull(MDC.get("traceId"));
        assertNull(MDC.get("otherKey"));
    }

    @Test
    @DisplayName("withTraceId_ShouldExecuteWithTraceIdAndRestore")
    void withTraceId_ShouldExecuteWithTraceIdAndRestore() {

        String originalTraceId = "original-trace-id";
        String temporaryTraceId = "temporary-trace-id";
        LoggingContext.setTraceId(originalTraceId);


        LoggingContext.withTraceId(temporaryTraceId, () -> {

            assertEquals(temporaryTraceId, LoggingContext.getTraceId());
        });

        assertEquals(originalTraceId, LoggingContext.getTraceId());
    }

    @Test
    @DisplayName("withTraceId_WithNoPreviousTraceId_ShouldClearAfterExecution")
    void withTraceId_WithNoPreviousTraceId_ShouldClearAfterExecution() {

        String temporaryTraceId = "temporary-trace-id";
        assertNull(LoggingContext.getTraceId()); // No previous trace ID

        LoggingContext.withTraceId(temporaryTraceId, () -> {

            assertEquals(temporaryTraceId, LoggingContext.getTraceId());
        });

        assertNull(LoggingContext.getTraceId());
    }

    @Test
    @DisplayName("withTraceId_WithException_ShouldStillRestorePreviousTraceId")
    void withTraceId_WithException_ShouldStillRestorePreviousTraceId() {

        String originalTraceId = "original-trace-id";
        String temporaryTraceId = "temporary-trace-id";
        LoggingContext.setTraceId(originalTraceId);


        assertThrows(RuntimeException.class, () -> {
            LoggingContext.withTraceId(temporaryTraceId, () -> {
                assertEquals(temporaryTraceId, LoggingContext.getTraceId());
                throw new RuntimeException("Test exception");
            });
        });

        assertEquals(originalTraceId, LoggingContext.getTraceId());
    }
}
