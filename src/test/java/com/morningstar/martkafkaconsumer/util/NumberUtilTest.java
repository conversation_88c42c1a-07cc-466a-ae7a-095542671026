package com.morningstar.martkafkaconsumer.util;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("NumberUtil Test")
class NumberUtilTest {

    // Test data for various long values
    private static final long[] TEST_LONG_VALUES = {
            0L,
            1L,
            -1L,
            Long.MAX_VALUE,
            Long.MIN_VALUE,
            123456789L,
            -987654321L,
            0x1234567890ABCDEFL
    };

    // Test data for various double values
    private static final double[] TEST_DOUBLE_VALUES = {
            0.0,
            1.0,
            -1.0,
            Double.MAX_VALUE,
            Double.MIN_VALUE,
            Double.POSITIVE_INFINITY,
            Double.NEGATIVE_INFINITY,
            Double.NaN,
            123.456,
            -987.654,
            3.141592653589793,
            2.718281828459045
    };

    @Test
    @DisplayName("Long to bytes conversion should work correctly")
    void testLongToBytes() {
        for (long value : TEST_LONG_VALUES) {
            byte[] bytes = NumberUtil.longToBytes(value);

            // Verify byte array length
            assertEquals(8, bytes.length, "Byte array should be 8 bytes long");

            // Verify round-trip conversion
            long converted = NumberUtil.bytesToLong(bytes);
            assertEquals(value, converted, "Round-trip conversion should preserve value: " + value);
        }
    }

    @Test
    @DisplayName("Bytes to long conversion should work correctly")
    void testBytesToLong() {
        // Test with known byte array
        byte[] bytes = {0x12, 0x34, 0x56, 0x78, (byte)0x9A, (byte)0xBC, (byte)0xDE, (byte)0xF0};
        long expected = 0x123456789ABCDEF0L;

        long result = NumberUtil.bytesToLong(bytes);
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("Bytes to long with default value should handle null input")
    void testBytesToLongWithDefaultValue_NullInput() {
        long defaultValue = 12345L;
        long result = NumberUtil.bytesToLong(null, defaultValue);
        assertEquals(defaultValue, result);
    }

    @Test
    @DisplayName("Bytes to long with default value should handle invalid length")
    void testBytesToLongWithDefaultValue_InvalidLength() {
        long defaultValue = 54321L;

        // Test with too short array
        byte[] shortBytes = {0x01, 0x02, 0x03};
        long result = NumberUtil.bytesToLong(shortBytes, defaultValue);
        assertEquals(defaultValue, result);

        // Test with too long array
        byte[] longBytes = {0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09};
        result = NumberUtil.bytesToLong(longBytes, defaultValue);
        assertEquals(defaultValue, result);
    }

    @Test
    @DisplayName("Bytes to long should throw exception for null input")
    void testBytesToLong_NullInput() {
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> NumberUtil.bytesToLong(null)
        );
        assertTrue(exception.getMessage().contains("Byte array must be exactly 8 bytes"));
    }

    @Test
    @DisplayName("Bytes to long should throw exception for invalid length")
    void testBytesToLong_InvalidLength() {
        // Test with too short array
        byte[] shortBytes = {0x01, 0x02, 0x03};
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> NumberUtil.bytesToLong(shortBytes)
        );
        assertTrue(exception.getMessage().contains("Byte array must be exactly 8 bytes"));

        // Test with too long array
        byte[] longBytes = new byte[10];
        exception = assertThrows(
                IllegalArgumentException.class,
                () -> NumberUtil.bytesToLong(longBytes)
        );
        assertTrue(exception.getMessage().contains("Byte array must be exactly 8 bytes"));
    }

    @Test
    @DisplayName("Multiple longs to bytes conversion should work correctly")
    void testLongsToBytes() {
        long[] longs = {123L, 456L, 789L};
        byte[] bytes = NumberUtil.longsToBytes(longs);

        // Should be 3 * 8 = 24 bytes
        assertEquals(24, bytes.length);

        // Verify each long can be extracted correctly
        byte[] firstLongBytes = new byte[8];
        System.arraycopy(bytes, 0, firstLongBytes, 0, 8);
        assertEquals(123L, NumberUtil.bytesToLong(firstLongBytes));

        byte[] secondLongBytes = new byte[8];
        System.arraycopy(bytes, 8, secondLongBytes, 0, 8);
        assertEquals(456L, NumberUtil.bytesToLong(secondLongBytes));

        byte[] thirdLongBytes = new byte[8];
        System.arraycopy(bytes, 16, thirdLongBytes, 0, 8);
        assertEquals(789L, NumberUtil.bytesToLong(thirdLongBytes));
    }

    @Test
    @DisplayName("Single long in longsToBytes should use bit shifting optimization")
    void testLongsToBytes_SingleValue() {
        long value = 123456789L;

        // Both methods should produce the same result
        byte[] directResult = NumberUtil.longToBytes(value);
        byte[] varargsResult = NumberUtil.longsToBytes(value);

        assertArrayEquals(directResult, varargsResult);
    }

    @Test
    @DisplayName("Empty longs array should produce empty bytes array")
    void testLongsToBytes_EmptyArray() {
        long[] emptyArray = {};
        byte[] result = NumberUtil.longsToBytes(emptyArray);
        assertEquals(0, result.length);
    }

    @Test
    @DisplayName("Double to bytes conversion should work correctly")
    void testDoubleToBytes() {
        for (double value : TEST_DOUBLE_VALUES) {
            byte[] bytes = NumberUtil.doubleToBytes(value);

            // Verify byte array length
            assertEquals(8, bytes.length, "Byte array should be 8 bytes long");

            // Verify round-trip conversion
            double converted = NumberUtil.bytesToDouble(bytes);
            if (Double.isNaN(value)) {
                assertTrue(Double.isNaN(converted), "NaN should remain NaN");
            } else {
                assertEquals(value, converted, "Round-trip conversion should preserve value: " + value);
            }
        }
    }

    @Test
    @DisplayName("Bytes to double conversion should work correctly")
    void testBytesToDouble() {
        // Test with known byte array for double 123.456
        double expectedValue = 123.456;
        byte[] bytes = NumberUtil.doubleToBytes(expectedValue);

        double result = NumberUtil.bytesToDouble(bytes);
        assertEquals(expectedValue, result);
    }

    @Test
    @DisplayName("Bytes to double with default value should handle null input")
    void testBytesToDoubleWithDefaultValue_NullInput() {
        double defaultValue = 3.14159;
        double result = NumberUtil.bytesToDouble(null, defaultValue);
        assertEquals(defaultValue, result);
    }

    @Test
    @DisplayName("Bytes to double with default value should handle invalid length")
    void testBytesToDoubleWithDefaultValue_InvalidLength() {
        double defaultValue = 2.71828;

        // Test with too short array
        byte[] shortBytes = {0x01, 0x02, 0x03};
        double result = NumberUtil.bytesToDouble(shortBytes, defaultValue);
        assertEquals(defaultValue, result);

        // Test with too long array
        byte[] longBytes = {0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09};
        result = NumberUtil.bytesToDouble(longBytes, defaultValue);
        assertEquals(defaultValue, result);
    }

    @Test
    @DisplayName("Bytes to double should throw exception for null input")
    void testBytesToDouble_NullInput() {
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> NumberUtil.bytesToDouble(null)
        );
        assertTrue(exception.getMessage().contains("Byte array must be exactly 8 bytes"));
    }

    @Test
    @DisplayName("Bytes to double should throw exception for invalid length")
    void testBytesToDouble_InvalidLength() {
        // Test with too short array
        byte[] shortBytes = {0x01, 0x02, 0x03};
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> NumberUtil.bytesToDouble(shortBytes)
        );
        assertTrue(exception.getMessage().contains("Byte array must be exactly 8 bytes"));

        // Test with empty array
        byte[] emptyBytes = {};
        exception = assertThrows(
                IllegalArgumentException.class,
                () -> NumberUtil.bytesToDouble(emptyBytes)
        );
        assertTrue(exception.getMessage().contains("Byte array must be exactly 8 bytes"));
    }

    @Test
    @DisplayName("Multiple doubles to bytes conversion should work correctly")
    void testDoublesToBytes() {
        double[] doubles = {123.456, 789.012, -345.678};
        byte[] bytes = NumberUtil.doublesToBytes(doubles);

        // Should be 3 * 8 = 24 bytes
        assertEquals(24, bytes.length);

        // Verify each double can be extracted correctly (manual extraction for testing)
        byte[] firstDoubleBytes = new byte[8];
        System.arraycopy(bytes, 0, firstDoubleBytes, 0, 8);
        assertEquals(123.456, NumberUtil.bytesToDouble(firstDoubleBytes));

        byte[] secondDoubleBytes = new byte[8];
        System.arraycopy(bytes, 8, secondDoubleBytes, 0, 8);
        assertEquals(789.012, NumberUtil.bytesToDouble(secondDoubleBytes));

        byte[] thirdDoubleBytes = new byte[8];
        System.arraycopy(bytes, 16, thirdDoubleBytes, 0, 8);
        assertEquals(-345.678, NumberUtil.bytesToDouble(thirdDoubleBytes));
    }

    @Test
    @DisplayName("Single double in doublesToBytes should use bit shifting optimization")
    void testDoublesToBytes_SingleValue() {
        double value = 123.456789;

        // Both methods should produce the same result
        byte[] directResult = NumberUtil.doubleToBytes(value);
        byte[] varargsResult = NumberUtil.doublesToBytes(value);

        assertArrayEquals(directResult, varargsResult);
    }

    @Test
    @DisplayName("Empty doubles array should produce empty bytes array")
    void testDoublesToBytes_EmptyArray() {
        double[] emptyArray = {};
        byte[] result = NumberUtil.doublesToBytes(emptyArray);
        assertEquals(0, result.length);
    }

    @ParameterizedTest
    @ValueSource(longs = {0L, 1L, -1L, Long.MAX_VALUE, Long.MIN_VALUE, 123456789L})
    @DisplayName("Parameterized test for long round-trip conversion")
    void testLongRoundTripConversion(long value) {
        byte[] bytes = NumberUtil.longToBytes(value);
        long converted = NumberUtil.bytesToLong(bytes);
        assertEquals(value, converted);
    }

    @ParameterizedTest
    @ValueSource(doubles = {0.0, 1.0, -1.0, 123.456, -789.012, 3.141592653589793})
    @DisplayName("Parameterized test for double round-trip conversion")
    void testDoubleRoundTripConversion(double value) {
        byte[] bytes = NumberUtil.doubleToBytes(value);
        double converted = NumberUtil.bytesToDouble(bytes);
        assertEquals(value, converted);
    }

    @Test
    @DisplayName("Test special double values")
    void testSpecialDoubleValues() {
        // Test positive and negative infinity
        byte[] posInfBytes = NumberUtil.doubleToBytes(Double.POSITIVE_INFINITY);
        assertEquals(Double.POSITIVE_INFINITY, NumberUtil.bytesToDouble(posInfBytes));

        byte[] negInfBytes = NumberUtil.doubleToBytes(Double.NEGATIVE_INFINITY);
        assertEquals(Double.NEGATIVE_INFINITY, NumberUtil.bytesToDouble(negInfBytes));

        // Test NaN
        byte[] nanBytes = NumberUtil.doubleToBytes(Double.NaN);
        assertTrue(Double.isNaN(NumberUtil.bytesToDouble(nanBytes)));

        // Test MIN_VALUE and MAX_VALUE
        byte[] minBytes = NumberUtil.doubleToBytes(Double.MIN_VALUE);
        assertEquals(Double.MIN_VALUE, NumberUtil.bytesToDouble(minBytes));

        byte[] maxBytes = NumberUtil.doubleToBytes(Double.MAX_VALUE);
        assertEquals(Double.MAX_VALUE, NumberUtil.bytesToDouble(maxBytes));
    }

    @Test
    @DisplayName("Test consistency between long and double bit operations")
    void testLongDoubleConsistency() {
        // Test that double conversion through long bits is consistent
        double originalDouble = 123.456789;
        long longBits = Double.doubleToRawLongBits(originalDouble);

        // Convert long to bytes and back
        byte[] longBytes = NumberUtil.longToBytes(longBits);
        long recoveredLongBits = NumberUtil.bytesToLong(longBytes);

        // Convert double to bytes and back
        byte[] doubleBytes = NumberUtil.doubleToBytes(originalDouble);
        double recoveredDouble = NumberUtil.bytesToDouble(doubleBytes);

        // They should be consistent
        assertEquals(longBits, recoveredLongBits);
        assertEquals(originalDouble, recoveredDouble);

        // The bytes should be the same
        assertArrayEquals(longBytes, doubleBytes);
    }

    @Test
    @DisplayName("Performance baseline test - verify optimization paths are taken")
    void testOptimizationPaths() {
        // Test single value optimization for longs
        long singleLong = 123456789L;
        byte[] singleLongBytes = NumberUtil.longsToBytes(singleLong);
        byte[] directLongBytes = NumberUtil.longToBytes(singleLong);
        assertArrayEquals(directLongBytes, singleLongBytes, "Single long should use optimized path");

        // Test single value optimization for doubles
        double singleDouble = 123.456;
        byte[] singleDoubleBytes = NumberUtil.doublesToBytes(singleDouble);
        byte[] directDoubleBytes = NumberUtil.doubleToBytes(singleDouble);
        assertArrayEquals(directDoubleBytes, singleDoubleBytes, "Single double should use optimized path");

        // Test multiple values use different path (this is more of a code coverage test)
        long[] multipleLongs = {123L, 456L};
        byte[] multipleLongBytes = NumberUtil.longsToBytes(multipleLongs);
        assertEquals(16, multipleLongBytes.length, "Multiple longs should produce correct byte count");

        double[] multipleDoubles = {123.456, 789.012};
        byte[] multipleDoubleBytes = NumberUtil.doublesToBytes(multipleDoubles);
        assertEquals(16, multipleDoubleBytes.length, "Multiple doubles should produce correct byte count");
    }

    @Test
    @DisplayName("Test edge cases for time series data scenarios")
    void testTimeSeriesDataScenarios() {
        // Based on your TsOldRspGatewayImpl.java, test scenarios specific to time series data

        // Test converting epoch days (as used in your formatDate method)
        long epochDay = LocalDate.now().toEpochDay();
        byte[] epochBytes = NumberUtil.longToBytes(epochDay);
        long recoveredEpoch = NumberUtil.bytesToLong(epochBytes);
        assertEquals(epochDay, recoveredEpoch, "Epoch day conversion should be accurate");

        // Test price values (common in financial time series)
        double[] prices = {0.0, 0.01, 999999.99, -123.456, Double.MAX_VALUE};
        for (double price : prices) {
            byte[] priceBytes = NumberUtil.doubleToBytes(price);
            double recoveredPrice = NumberUtil.bytesToDouble(priceBytes);
            assertEquals(price, recoveredPrice, "Price value should be preserved: " + price);
        }

        // Test batch conversion for multiple prices (optimization test)
        double[] multiplePrices = {100.50, 200.75, 300.25, 400.00};
        byte[] batchBytes = NumberUtil.doublesToBytes(multiplePrices);
        assertEquals(32, batchBytes.length, "Batch price conversion should produce correct byte count");

        // Verify each price can be recovered
        for (int i = 0; i < multiplePrices.length; i++) {
            byte[] singlePriceBytes = new byte[8];
            System.arraycopy(batchBytes, i * 8, singlePriceBytes, 0, 8);
            double recoveredPrice = NumberUtil.bytesToDouble(singlePriceBytes);
            assertEquals(multiplePrices[i], recoveredPrice, "Batch price at index " + i + " should be recoverable");
        }
    }
}
