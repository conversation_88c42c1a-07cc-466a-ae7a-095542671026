package com.morningstar.martkafkaconsumer.consumer;

import com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent;
import com.morningstar.data.domain.ics.eod.enriched.CopyOverReasonEnum;
import com.morningstar.data.domain.ics.eod.enriched.MarketPriceDetail;
import com.morningstar.martkafkaconsumer.exception.MessageProcessingException;
import com.morningstar.martkafkaconsumer.service.MarketPriceDataService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.CompletionException;
import java.util.concurrent.ExecutorService;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for MarketPriceEnrichedConsumer
 * Simple and maintainable tests using mocks
 */
@ExtendWith(MockitoExtension.class)
class MarketPriceEnrichedConsumerTest {

    @Mock
    private KafkaConsumerEndpoint<Long, MarketPriceEnrichedEvent> mockKafkaConsumerEndpoint;

    @Mock
    private MarketPriceDataService mockMarketPriceDataService;

    @Mock
    private ExecutorService mockExecutorService;

    private MarketPriceEnrichedConsumer consumer;

    @BeforeEach
    void setUp() {
        consumer = new MarketPriceEnrichedConsumer(
            mockKafkaConsumerEndpoint,
            mockMarketPriceDataService,
            mockExecutorService
        );
    }


    @Test
    void processMessages_WithValidMessages_ShouldProcessAllMessages() {
        // Arrange
        Collection<MarketPriceEnrichedEvent> messages = createTestMessages();
        String pollId = "test-poll-id-002";

        // Mock ExecutorService to run tasks synchronously
        doAnswer(invocation -> {
            Runnable task = invocation.getArgument(0);
            task.run();
            return null;
        }).when(mockExecutorService).execute(any(Runnable.class));

        // Act
        assertDoesNotThrow(() -> consumer.processMessages(messages, pollId));

        // Assert
        verify(mockMarketPriceDataService, times(2)).storeMarketPriceDetail(any(MarketPriceDetail.class));
    }

    @Test
    void processMessage_WithNullMarketPriceDataList_ShouldSkipProcessing() {
        // Arrange
        MarketPriceEnrichedEvent event = new MarketPriceEnrichedEvent();
        event.setMarketPriceDataList(null);

        Collection<MarketPriceEnrichedEvent> messages = List.of(event);
        String pollId = "test-poll-id-003";

        // Mock ExecutorService to run tasks synchronously
        doAnswer(invocation -> {
            Runnable task = invocation.getArgument(0);
            task.run();
            return null;
        }).when(mockExecutorService).execute(any(Runnable.class));

        // Act
        assertDoesNotThrow(() -> consumer.processMessages(messages, pollId));

        // Assert
        verify(mockMarketPriceDataService, never()).storeMarketPriceDetail(any(MarketPriceDetail.class));
    }

    @Test
    void processMessage_WithEmptyMarketPriceDataList_ShouldSkipProcessing() {
        // Arrange
        MarketPriceEnrichedEvent event = new MarketPriceEnrichedEvent();
        event.setMarketPriceDataList(new ArrayList<>());

        Collection<MarketPriceEnrichedEvent> messages = List.of(event);
        String pollId = "test-poll-id-004";

        // Mock ExecutorService to run tasks synchronously
        doAnswer(invocation -> {
            Runnable task = invocation.getArgument(0);
            task.run();
            return null;
        }).when(mockExecutorService).execute(any(Runnable.class));

        // Act
        assertDoesNotThrow(() -> consumer.processMessages(messages, pollId));

        // Assert
        verify(mockMarketPriceDataService, never()).storeMarketPriceDetail(any(MarketPriceDetail.class));
    }

    @Test
    void processMessage_WithServiceException_ShouldThrowCompletionException() {
        // Arrange
        Collection<MarketPriceEnrichedEvent> messages = createTestMessages();
        String pollId = "test-poll-id-005";

        // Mock service to throw exception
        doThrow(new RuntimeException("Service error")).when(mockMarketPriceDataService)
            .storeMarketPriceDetail(any(MarketPriceDetail.class));

        // Mock ExecutorService to run tasks synchronously
        doAnswer(invocation -> {
            Runnable task = invocation.getArgument(0);
            task.run();
            return null;
        }).when(mockExecutorService).execute(any(Runnable.class));

        // Act & Assert - should throw CompletionException wrapping MessageProcessingException
        CompletionException exception = assertThrows(CompletionException.class,
            () -> consumer.processMessages(messages, pollId));

        // Verify the cause is our custom MessageProcessingException
        assertInstanceOf(MessageProcessingException.class, exception.getCause());
        assertEquals("MarketPriceEnrichedConsumer: Failed to process message", exception.getCause().getMessage());

        // Assert
        verify(mockMarketPriceDataService, times(2)).storeMarketPriceDetail(any(MarketPriceDetail.class));
    }

    @Test
    void processMessages_WithValidData_ShouldSetCorrectRedisKey() {
        // Arrange
        Collection<MarketPriceEnrichedEvent> messages = createTestMessages();
        String pollId = "test-poll-id-006";

        // Mock ExecutorService to run tasks synchronously
        doAnswer(invocation -> {
            Runnable task = invocation.getArgument(0);
            task.run();
            return null;
        }).when(mockExecutorService).execute(any(Runnable.class));

        // Act
        consumer.processMessages(messages, pollId);

    }

    @Test
    void processMessages_WithMultipleMarketPriceDetails_ShouldProcessAllDetails() {
        // Arrange
        MarketPriceEnrichedEvent event = new MarketPriceEnrichedEvent();
        List<MarketPriceDetail> marketPriceDetails = new ArrayList<>();

        // Add multiple market price details
        marketPriceDetails.add(createTestMarketPriceDetail("PERF001", LocalDate.of(2023, 1, 1)));
        marketPriceDetails.add(createTestMarketPriceDetail("PERF002", LocalDate.of(2023, 1, 2)));
        marketPriceDetails.add(createTestMarketPriceDetail("PERF003", LocalDate.of(2023, 1, 3)));

        event.setMarketPriceDataList(marketPriceDetails);
        Collection<MarketPriceEnrichedEvent> messages = List.of(event);
        String pollId = "test-poll-id-007";

        // Mock ExecutorService to run tasks synchronously
        doAnswer(invocation -> {
            Runnable task = invocation.getArgument(0);
            task.run();
            return null;
        }).when(mockExecutorService).execute(any(Runnable.class));

        // Act
        consumer.processMessages(messages, pollId);

        // Assert - verify all market price details are processed
        verify(mockMarketPriceDataService, times(3)).storeMarketPriceDetail(any(MarketPriceDetail.class));
    }

    /**
     * Helper method to create test messages
     */
    private Collection<MarketPriceEnrichedEvent> createTestMessages() {
        List<MarketPriceEnrichedEvent> messages = new ArrayList<>();
        
        // Create first message with one market price detail
        MarketPriceEnrichedEvent event1 = new MarketPriceEnrichedEvent();
        List<MarketPriceDetail> details1 = new ArrayList<>();
        details1.add(createTestMarketPriceDetail("PERF001", LocalDate.of(2023, 6, 15)));
        event1.setMarketPriceDataList(details1);
        
        // Create second message with one market price detail
        MarketPriceEnrichedEvent event2 = new MarketPriceEnrichedEvent();
        List<MarketPriceDetail> details2 = new ArrayList<>();
        details2.add(createTestMarketPriceDetail("PERF002", LocalDate.of(2023, 6, 16)));
        event2.setMarketPriceDataList(details2);
        
        messages.add(event1);
        messages.add(event2);
        
        return messages;
    }

    /**
     * Helper method to create test market price detail
     */
    private MarketPriceDetail createTestMarketPriceDetail(String performanceId, LocalDate date) {
        MarketPriceDetail detail = new MarketPriceDetail();
        detail.setPerformanceId(performanceId);
        detail.setDate(date);
        detail.setHigh(BigDecimal.valueOf(100.50));
        detail.setLow(BigDecimal.valueOf(98.25));
        detail.setOpen(BigDecimal.valueOf(99.00));
        detail.setClose(BigDecimal.valueOf(100.00));
        detail.setTradedVolume(BigDecimal.valueOf(1000000));
        detail.setCopyOverReason(CopyOverReasonEnum.PRICE);
        return detail;
    }
}
