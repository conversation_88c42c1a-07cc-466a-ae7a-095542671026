package com.morningstar.martkafkaconsumer.config;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.concurrent.ExecutorService;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for ExecutorConfig
 */
@ExtendWith(MockitoExtension.class)
class ExecutorConfigTest {

    @Mock
    private ExecutorProperties executorProperties;

    @Mock
    private ExecutorProperties.MarketPrice marketPrice;

    private ExecutorConfig executorConfig;

    @BeforeEach
    void setUp() {
        when(executorProperties.getMarketPrice()).thenReturn(marketPrice);
        executorConfig = new ExecutorConfig(executorProperties);
    }

    @Test
    @DisplayName("marketPriceExecutor should create ExecutorService with configured thread count")
    void testMarketPriceExecutorWithConfiguredThreadCount() {
        // Given
        int expectedThreadCount = 12;
        when(marketPrice.getThreadCount()).thenReturn(expectedThreadCount);

        // When
        ExecutorService executor = executorConfig.marketPriceExecutor();

        // Then
        assertNotNull(executor);
        assertFalse(executor.isShutdown());
        
        // Verify the properties were accessed
        verify(executorProperties).getMarketPrice();
        verify(marketPrice).getThreadCount();
        
        // Clean up
        executor.shutdown();
    }

    @Test
    @DisplayName("marketPriceExecutor should handle default thread count")
    void testMarketPriceExecutorWithDefaultThreadCount() {
        // Given
        int defaultThreadCount = Runtime.getRuntime().availableProcessors();
        when(marketPrice.getThreadCount()).thenReturn(defaultThreadCount);

        // When
        ExecutorService executor = executorConfig.marketPriceExecutor();

        // Then
        assertNotNull(executor);
        assertFalse(executor.isShutdown());
        
        // Verify the properties were accessed
        verify(executorProperties).getMarketPrice();
        verify(marketPrice).getThreadCount();
        
        // Clean up
        executor.shutdown();
    }

    @Test
    @DisplayName("marketPriceExecutor should handle single thread configuration")
    void testMarketPriceExecutorWithSingleThread() {
        // Given
        when(marketPrice.getThreadCount()).thenReturn(1);

        // When
        ExecutorService executor = executorConfig.marketPriceExecutor();

        // Then
        assertNotNull(executor);
        assertFalse(executor.isShutdown());
        
        // Clean up
        executor.shutdown();
    }

    @Test
    @DisplayName("ExecutorConfig constructor should accept ExecutorProperties")
    void testExecutorConfigConstructor() {
        // When
        ExecutorConfig config = new ExecutorConfig(executorProperties);

        // Then
        assertNotNull(config);
    }
}
