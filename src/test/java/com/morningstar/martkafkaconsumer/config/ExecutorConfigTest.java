package com.morningstar.martkafkaconsumer.config;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.util.concurrent.ExecutorService;

import static org.junit.jupiter.api.Assertions.*;


@SpringBootTest(classes = ExecutorConfig.class)
@TestPropertySource(properties = {"executor.thread-count=16"})
class ExecutorConfigTest {

    @Autowired
    private ExecutorConfig executorConfig;

    @Test
    @DisplayName("marketPriceExecutor should create ExecutorService with configured thread count")
    void testMarketPriceExecutor_WithConfiguredThreadCount() {
        // When
        ExecutorService executor = executorConfig.marketPriceExecutor();

        // Then
        assertNotNull(executor);
        assertFalse(executor.isShutdown());
        
        // Clean up
        executor.shutdown();
    }
}
