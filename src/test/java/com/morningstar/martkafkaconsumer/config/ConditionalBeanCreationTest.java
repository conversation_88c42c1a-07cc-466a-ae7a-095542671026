package com.morningstar.martkafkaconsumer.config;

import com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer;
import com.morningstar.martkafkaconsumer.consumer.MarketPriceRawConsumer;
import com.morningstar.martkafkaconsumer.service.MarketPriceDataService;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.TestPropertySource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Integration tests for conditional bean creation based on kafka.active-consume property
 */
class ConditionalBeanCreationTest {

    @SpringBootTest
    @TestPropertySource(properties = {"kafka.active-consume=marketPriceEnrichedConsumer"})
    static class MarketPriceEnrichedConsumerActiveTest {
        
        @Test
        @DisplayName("Should create marketPriceEnrichedConsumer beans when active-consume is marketPriceEnrichedConsumer")
        void testMarketPriceEnrichedConsumerBeansCreated(ApplicationContext context) {
            // Should create enriched consumer related beans
            assertTrue(context.containsBean("marketPriceEnrichedConsumer"));
            assertTrue(context.containsBean("marketPriceEnrichedEventEndpoint"));
            assertTrue(context.containsBean("adjustedPriceDataService"));
            
            // Should NOT create raw consumer related beans
            assertFalse(context.containsBean("marketPriceRawConsumer"));
            assertFalse(context.containsBean("marketPriceListUpdatedEndpoint"));
            assertFalse(context.containsBean("rawPriceDataService"));
        }
    }

    @SpringBootTest
    @TestPropertySource(properties = {"kafka.active-consume=marketPriceRawConsumer"})
    static class MarketPriceRawConsumerActiveTest {
        
        @Test
        @DisplayName("Should create marketPriceRawConsumer beans when active-consume is marketPriceRawConsumer")
        void testMarketPriceRawConsumerBeansCreated(ApplicationContext context) {
            // Should create raw consumer related beans
            assertTrue(context.containsBean("marketPriceRawConsumer"));
            assertTrue(context.containsBean("marketPriceListUpdatedEndpoint"));
            assertTrue(context.containsBean("rawPriceDataService"));
            
            // Should NOT create enriched consumer related beans
            assertFalse(context.containsBean("marketPriceEnrichedConsumer"));
            assertFalse(context.containsBean("marketPriceEnrichedEventEndpoint"));
            assertFalse(context.containsBean("adjustedPriceDataService"));
        }
    }

    @SpringBootTest
    @TestPropertySource(properties = {"kafka.active-consume=invalidValue"})
    static class InvalidActiveConsumeTest {
        
        @Test
        @DisplayName("Should not create any consumer beans when active-consume has invalid value")
        void testNoConsumerBeansCreatedWithInvalidValue(ApplicationContext context) {
            // Should NOT create any consumer related beans
            assertFalse(context.containsBean("marketPriceEnrichedConsumer"));
            assertFalse(context.containsBean("marketPriceEnrichedEventEndpoint"));
            assertFalse(context.containsBean("adjustedPriceDataService"));
            assertFalse(context.containsBean("marketPriceRawConsumer"));
            assertFalse(context.containsBean("marketPriceListUpdatedEndpoint"));
            assertFalse(context.containsBean("rawPriceDataService"));
            
            // Common beans should still be created
            assertTrue(context.containsBean("tsRepo"));
            assertTrue(context.containsBean("marketPriceExecutor"));
        }
    }
}
