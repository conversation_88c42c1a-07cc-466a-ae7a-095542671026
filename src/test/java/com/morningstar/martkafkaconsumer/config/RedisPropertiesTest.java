package com.morningstar.martkafkaconsumer.config;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for RedisProperties and TsData
 */
class RedisPropertiesTest {

    private RedisProperties redisProperties;

    @BeforeEach
    void setUp() {
        redisProperties = new RedisProperties();
    }

    @Test
    @DisplayName("RedisProperties should have correct default values")
    void testRedisPropertiesDefaults() {
        assertNotNull(redisProperties.getTsData());
        assertNull(redisProperties.getClientName());
    }

    @Test
    @DisplayName("RedisProperties setters should work correctly")
    void testRedisPropertiesSetters() {
        String clientName = "test-client";
        redisProperties.setClientName(clientName);
        assertEquals(clientName, redisProperties.getClientName());

        RedisProperties.TsData newTsData = new RedisProperties.TsData();
        newTsData.setHost("new-host");
        newTsData.setPort(9999);
        
        redisProperties.setTsData(newTsData);
        assertEquals(newTsData, redisProperties.getTsData());
        assertEquals("new-host", redisProperties.getTsData().getHost());
        assertEquals(9999, redisProperties.getTsData().getPort());
    }

    @Test
    @DisplayName("TsData should have correct default values")
    void testTsDataDefaults() {
        RedisProperties.TsData tsData = new RedisProperties.TsData();
        assertNull(tsData.getHost());
        assertEquals(0, tsData.getPort()); // int default value
    }

    @Test
    @DisplayName("TsData setters should work correctly")
    void testTsDataSetters() {
        RedisProperties.TsData tsData = new RedisProperties.TsData();
        
        String host = "ts-data-v8-cluster-stg.bg4uxb.clustercfg.use1.cache.amazonaws.com";
        int port = 6379;

        tsData.setHost(host);
        tsData.setPort(port);

        assertEquals(host, tsData.getHost());
        assertEquals(port, tsData.getPort());
    }

    @Test
    @DisplayName("RedisProperties should handle null values gracefully")
    void testRedisPropertiesNullValues() {
        redisProperties.setClientName(null);
        assertNull(redisProperties.getClientName());

        redisProperties.setTsData(null);
        assertNull(redisProperties.getTsData());
    }

    @Test
    @DisplayName("TsData should handle null values gracefully")
    void testTsDataNullValues() {
        RedisProperties.TsData tsData = new RedisProperties.TsData();
        
        tsData.setHost(null);
        assertNull(tsData.getHost());
        
        // Port is primitive int, so it can't be null, but we can test edge values
        tsData.setPort(-1);
        assertEquals(-1, tsData.getPort());
        
        tsData.setPort(0);
        assertEquals(0, tsData.getPort());
    }

    @Test
    @DisplayName("RedisProperties should handle empty values")
    void testRedisPropertiesEmptyValues() {
        redisProperties.setClientName("");
        assertEquals("", redisProperties.getClientName());
    }

    @Test
    @DisplayName("TsData should handle empty values")
    void testTsDataEmptyValues() {
        RedisProperties.TsData tsData = new RedisProperties.TsData();
        
        tsData.setHost("");
        assertEquals("", tsData.getHost());
    }

    @Test
    @DisplayName("TsData should handle various port values")
    void testTsDataPortValues() {
        RedisProperties.TsData tsData = new RedisProperties.TsData();
        
        // Test common Redis port
        tsData.setPort(6379);
        assertEquals(6379, tsData.getPort());
        
        // Test alternative port
        tsData.setPort(6380);
        assertEquals(6380, tsData.getPort());
        
        // Test high port number
        tsData.setPort(65535);
        assertEquals(65535, tsData.getPort());
        
        // Test minimum port
        tsData.setPort(1);
        assertEquals(1, tsData.getPort());
        
        // Test zero port
        tsData.setPort(0);
        assertEquals(0, tsData.getPort());
        
        // Test negative port (edge case)
        tsData.setPort(-1);
        assertEquals(-1, tsData.getPort());
    }

    @Test
    @DisplayName("RedisProperties toString should work correctly")
    void testRedisPropertiesToString() {
        redisProperties.setClientName("test-client");
        redisProperties.getTsData().setHost("test-host");
        redisProperties.getTsData().setPort(6379);
        
        String toString = redisProperties.toString();
        assertNotNull(toString);
        assertTrue(toString.contains("RedisProperties"));
        assertTrue(toString.contains("clientName"));
        assertTrue(toString.contains("tsData"));
    }

    @Test
    @DisplayName("TsData toString should work correctly")
    void testTsDataToString() {
        RedisProperties.TsData tsData = new RedisProperties.TsData();
        tsData.setHost("test-host");
        tsData.setPort(6379);
        
        String toString = tsData.toString();
        assertNotNull(toString);
        assertTrue(toString.contains("TsData"));
        assertTrue(toString.contains("host"));
        assertTrue(toString.contains("port"));
    }

    @Test
    @DisplayName("RedisProperties equals and hashCode should work correctly")
    void testRedisPropertiesEqualsAndHashCode() {
        RedisProperties props1 = new RedisProperties();
        RedisProperties props2 = new RedisProperties();

        // Test equality with default values
        assertEquals(props1, props2);
        assertEquals(props1.hashCode(), props2.hashCode());

        // Test inequality after modification
        props1.setClientName("test-client");
        assertNotEquals(props1, props2);

        // Test equality after same modification
        props2.setClientName("test-client");
        assertEquals(props1, props2);
        assertEquals(props1.hashCode(), props2.hashCode());

        // Test inequality after TsData modification
        props1.getTsData().setHost("test-host");
        assertNotEquals(props1, props2);

        // Test equality after same TsData modification
        props2.getTsData().setHost("test-host");
        assertEquals(props1, props2);
        assertEquals(props1.hashCode(), props2.hashCode());
    }

    @Test
    @DisplayName("TsData equals and hashCode should work correctly")
    void testTsDataEqualsAndHashCode() {
        RedisProperties.TsData tsData1 = new RedisProperties.TsData();
        RedisProperties.TsData tsData2 = new RedisProperties.TsData();

        // Test equality with default values
        assertEquals(tsData1, tsData2);
        assertEquals(tsData1.hashCode(), tsData2.hashCode());

        // Test inequality after host modification
        tsData1.setHost("test-host");
        assertNotEquals(tsData1, tsData2);

        // Test equality after same host modification
        tsData2.setHost("test-host");
        assertEquals(tsData1, tsData2);
        assertEquals(tsData1.hashCode(), tsData2.hashCode());

        // Test inequality after port modification
        tsData1.setPort(6379);
        assertNotEquals(tsData1, tsData2);

        // Test equality after same port modification
        tsData2.setPort(6379);
        assertEquals(tsData1, tsData2);
        assertEquals(tsData1.hashCode(), tsData2.hashCode());
    }

    @Test
    @DisplayName("RedisProperties should work with realistic configuration")
    void testRealisticConfiguration() {
        String expectedHost = "ts-data-v8-cluster-stg.bg4uxb.clustercfg.use1.cache.amazonaws.com";
        int expectedPort = 6379;
        String expectedClientName = "mart-kafka-consumer";

        redisProperties.setClientName(expectedClientName);
        redisProperties.getTsData().setHost(expectedHost);
        redisProperties.getTsData().setPort(expectedPort);

        assertEquals(expectedClientName, redisProperties.getClientName());
        assertEquals(expectedHost, redisProperties.getTsData().getHost());
        assertEquals(expectedPort, redisProperties.getTsData().getPort());
    }

    @Test
    @DisplayName("TsData should be independent instances")
    void testTsDataIndependence() {
        RedisProperties props1 = new RedisProperties();
        RedisProperties props2 = new RedisProperties();

        // Modify one instance
        props1.getTsData().setHost("host1");
        props1.getTsData().setPort(1111);

        // Verify the other instance is not affected
        assertNotEquals("host1", props2.getTsData().getHost());
        assertNotEquals(1111, props2.getTsData().getPort());

        // Verify they are different objects
        assertNotSame(props1.getTsData(), props2.getTsData());
    }

    @Test
    @DisplayName("RedisProperties should handle TsData replacement")
    void testTsDataReplacement() {
        RedisProperties.TsData originalTsData = redisProperties.getTsData();
        originalTsData.setHost("original-host");
        originalTsData.setPort(1111);

        RedisProperties.TsData newTsData = new RedisProperties.TsData();
        newTsData.setHost("new-host");
        newTsData.setPort(2222);

        redisProperties.setTsData(newTsData);

        assertEquals("new-host", redisProperties.getTsData().getHost());
        assertEquals(2222, redisProperties.getTsData().getPort());
        assertSame(newTsData, redisProperties.getTsData());
        assertNotSame(originalTsData, redisProperties.getTsData());
    }
}
