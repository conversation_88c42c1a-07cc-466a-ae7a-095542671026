package com.morningstar.martkafkaconsumer.config;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for ExecutorProperties
 */
class ExecutorPropertiesTest {

    private ExecutorProperties executorProperties;

    @BeforeEach
    void setUp() {
        executorProperties = new ExecutorProperties();
    }

    @Test
    @DisplayName("ExecutorProperties should have correct default values")
    void testExecutorPropertiesDefaults() {
        assertNotNull(executorProperties.getMarketPrice());
        assertEquals(Runtime.getRuntime().availableProcessors(), 
                    executorProperties.getMarketPrice().getThreadCount());
    }

    @Test
    @DisplayName("ExecutorProperties setters should work correctly")
    void testExecutorPropertiesSetters() {
        int expectedThreadCount = 16;
        
        ExecutorProperties.MarketPrice marketPrice = new ExecutorProperties.MarketPrice();
        marketPrice.setThreadCount(expectedThreadCount);
        executorProperties.setMarketPrice(marketPrice);

        assertEquals(expectedThreadCount, executorProperties.getMarketPrice().getThreadCount());
    }

    @Test
    @DisplayName("MarketPrice should handle different thread count values")
    void testMarketPriceThreadCountValues() {
        ExecutorProperties.MarketPrice marketPrice = executorProperties.getMarketPrice();
        
        // Test setting different values
        marketPrice.setThreadCount(1);
        assertEquals(1, marketPrice.getThreadCount());
        
        marketPrice.setThreadCount(4);
        assertEquals(4, marketPrice.getThreadCount());
        
        marketPrice.setThreadCount(32);
        assertEquals(32, marketPrice.getThreadCount());
    }

    @Test
    @DisplayName("ExecutorProperties should handle null MarketPrice")
    void testExecutorPropertiesWithNullMarketPrice() {
        executorProperties.setMarketPrice(null);
        assertNull(executorProperties.getMarketPrice());
    }

    @Test
    @DisplayName("MarketPrice default constructor should use available processors")
    void testMarketPriceDefaultConstructor() {
        ExecutorProperties.MarketPrice marketPrice = new ExecutorProperties.MarketPrice();
        assertEquals(Runtime.getRuntime().availableProcessors(), marketPrice.getThreadCount());
    }
}
