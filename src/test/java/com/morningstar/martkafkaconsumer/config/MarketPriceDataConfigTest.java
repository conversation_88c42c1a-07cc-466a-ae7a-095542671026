package com.morningstar.martkafkaconsumer.config;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class MarketPriceDataConfigTest {

    private MarketPriceDataConfig config;

    @BeforeEach
    void setUp() {
        config = new MarketPriceDataConfig();

        // Setup raw price data
        MarketPriceDataConfig.MarketPriceData rawPriceData = new MarketPriceDataConfig.MarketPriceData();
        rawPriceData.setRedisKeyPrefix("ts:MarketPrice:");
        rawPriceData.setYearsPerKey(20);
        rawPriceData.setYearsPerField(10);
        rawPriceData.setDataPointMapping(Map.of(
            "high", "EO001",
            "low", "EO002",
            "open", "EO003",
            "close", "EO004",
            "tradedVolume", "EO005"
        ));
        config.setRawPriceData(rawPriceData);

        // Setup adjusted price data
        MarketPriceDataConfig.MarketPriceData adjustedPriceData = new MarketPriceDataConfig.MarketPriceData();
        adjustedPriceData.setRedisKeyPrefix("ts:AdjustedMarketPrice:");
        adjustedPriceData.setYearsPerKey(20);
        adjustedPriceData.setYearsPerField(10);
        adjustedPriceData.setDataPointMapping(Map.of(
            "high", "Y2J44",
            "low", "OEFC5",
            "open", "LN98K",
            "close", "KXMBH",
            "tradedVolume", "EO005"
        ));
        config.setAdjustedPriceData(adjustedPriceData);
    }

    @Test
    @DisplayName("rawPriceData_WithValidConfiguration_ShouldLoadCorrectly")
    void rawPriceData_WithValidConfiguration_ShouldLoadCorrectly() {
        // Given & When
        MarketPriceDataConfig.MarketPriceData rawPriceData = config.getRawPriceData();

        // Then
        assertNotNull(rawPriceData);
        assertEquals("ts:MarketPrice:", rawPriceData.getRedisKeyPrefix());
        assertEquals(20, rawPriceData.getYearsPerKey());
        assertEquals(10, rawPriceData.getYearsPerField());
        
        Map<String, String> dataPointMapping = rawPriceData.getDataPointMapping();
        assertNotNull(dataPointMapping);
        assertEquals("EO001", dataPointMapping.get("high"));
        assertEquals("EO002", dataPointMapping.get("low"));
        assertEquals("EO003", dataPointMapping.get("open"));
        assertEquals("EO004", dataPointMapping.get("close"));
        assertEquals("EO005", dataPointMapping.get("tradedVolume"));
    }

    @Test
    @DisplayName("adjustedPriceData_WithValidConfiguration_ShouldLoadCorrectly")
    void adjustedPriceData_WithValidConfiguration_ShouldLoadCorrectly() {
        // Given & When
        MarketPriceDataConfig.MarketPriceData adjustedPriceData = config.getAdjustedPriceData();

        // Then
        assertNotNull(adjustedPriceData);
        assertEquals("ts:AdjustedMarketPrice:", adjustedPriceData.getRedisKeyPrefix());
        assertEquals(20, adjustedPriceData.getYearsPerKey());
        assertEquals(10, adjustedPriceData.getYearsPerField());
        
        Map<String, String> dataPointMapping = adjustedPriceData.getDataPointMapping();
        assertNotNull(dataPointMapping);
        assertEquals("Y2J44", dataPointMapping.get("high"));
        assertEquals("OEFC5", dataPointMapping.get("low"));
        assertEquals("LN98K", dataPointMapping.get("open"));
        assertEquals("KXMBH", dataPointMapping.get("close"));
        assertEquals("EO005", dataPointMapping.get("tradedVolume"));
    }

    @Test
    @DisplayName("marketPriceDataInnerClass_WithValidConfiguration_ShouldHaveCorrectStructure")
    void marketPriceDataInnerClass_WithValidConfiguration_ShouldHaveCorrectStructure() {
        // Given & When
        MarketPriceDataConfig.MarketPriceData rawData = config.getRawPriceData();
        MarketPriceDataConfig.MarketPriceData adjustedData = config.getAdjustedPriceData();

        // Then
        assertNotNull(rawData);
        assertNotNull(adjustedData);
        assertNotEquals(rawData.getRedisKeyPrefix(), adjustedData.getRedisKeyPrefix());
        
        // Verify they are different instances
        assertNotSame(rawData, adjustedData);
        
        // Verify both have the same structure but different values
        assertEquals(rawData.getYearsPerKey(), adjustedData.getYearsPerKey());
        assertEquals(rawData.getYearsPerField(), adjustedData.getYearsPerField());
        assertNotEquals(rawData.getDataPointMapping().get("high"), 
                       adjustedData.getDataPointMapping().get("high"));
    }
}
