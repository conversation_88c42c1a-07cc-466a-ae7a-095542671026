package com.morningstar.martkafkaconsumer.config;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for KafkaProperties and ConsumerEndpointConfig
 */
class KafkaPropertiesTest {

    private KafkaProperties kafkaProperties;

    @BeforeEach
    void setUp() {
        kafkaProperties = new KafkaProperties();
    }

    @Test
    @DisplayName("KafkaProperties should have correct default values")
    void testKafkaPropertiesDefaults() {
        assertNull(kafkaProperties.getRegion());
        assertNull(kafkaProperties.getEnvironment());
        assertNull(kafkaProperties.getClientPrincipal());
        assertNull(kafkaProperties.getKeytabRef());
        assertNull(kafkaProperties.getActiveConsumer());
        assertNull(kafkaProperties.getConsumerEndpoints());
    }

    @Test
    @DisplayName("KafkaProperties setters should work correctly")
    void testKafkaPropertiesSetters() {
        String region = "aws-us-east-1";
        String environment = "devcloud";
        String clientPrincipal = "svc-test-service";
        String keytabRef = "/path/to/keytab";
        String activeConsumer = "marketPriceEnrichedConsumer";

        kafkaProperties.setRegion(region);
        kafkaProperties.setEnvironment(environment);
        kafkaProperties.setClientPrincipal(clientPrincipal);
        kafkaProperties.setKeytabRef(keytabRef);
        kafkaProperties.setActiveConsumer(activeConsumer);

        assertEquals(region, kafkaProperties.getRegion());
        assertEquals(environment, kafkaProperties.getEnvironment());
        assertEquals(clientPrincipal, kafkaProperties.getClientPrincipal());
        assertEquals(keytabRef, kafkaProperties.getKeytabRef());
        assertEquals(activeConsumer, kafkaProperties.getActiveConsumer());
    }

    @Test
    @DisplayName("KafkaProperties should handle activeConsumer property")
    void testKafkaPropertiesActiveConsumer() {
        // Test marketPriceEnrichedConsumer
        kafkaProperties.setActiveConsumer("marketPriceEnrichedConsumer");
        assertEquals("marketPriceEnrichedConsumer", kafkaProperties.getActiveConsumer());

        // Test marketPriceRawConsumer
        kafkaProperties.setActiveConsumer("marketPriceRawConsumer");
        assertEquals("marketPriceRawConsumer", kafkaProperties.getActiveConsumer());

        // Test null value
        kafkaProperties.setActiveConsumer(null);
        assertNull(kafkaProperties.getActiveConsumer());
    }

    @Test
    @DisplayName("KafkaProperties should handle consumer endpoints")
    void testKafkaPropertiesConsumerEndpoints() {
        Map<String, KafkaProperties.ConsumerEndpointConfig> endpoints = new HashMap<>();
        
        KafkaProperties.ConsumerEndpointConfig config = new KafkaProperties.ConsumerEndpointConfig();
        config.setTopic("test-topic");
        config.setClientId("test-client");
        config.setGroupId("test-group");
        
        endpoints.put("test-endpoint", config);
        kafkaProperties.setConsumerEndpoints(endpoints);

        assertEquals(endpoints, kafkaProperties.getConsumerEndpoints());
        assertEquals(1, kafkaProperties.getConsumerEndpoints().size());
        assertTrue(kafkaProperties.getConsumerEndpoints().containsKey("test-endpoint"));
    }

    @Test
    @DisplayName("ConsumerEndpointConfig should have correct default values")
    void testConsumerEndpointConfigDefaults() {
        KafkaProperties.ConsumerEndpointConfig config = new KafkaProperties.ConsumerEndpointConfig();
        
        assertNull(config.getTopic());
        assertNull(config.getClientId());
        assertNull(config.getGroupId());
        assertNull(config.getKeyDeserializer());
        assertNull(config.getValueDeserializer());
        assertNull(config.getSerdePojoClass());
    }

    @Test
    @DisplayName("ConsumerEndpointConfig setters should work correctly")
    void testConsumerEndpointConfigSetters() {
        KafkaProperties.ConsumerEndpointConfig config = new KafkaProperties.ConsumerEndpointConfig();
        
        String topic = "market_price_enriched_event";
        String clientId = "DPDA_Market_Data_Service";
        String groupId = "dpda_market_price_group";
        String keyDeserializer = "org.apache.kafka.common.serialization.LongDeserializer";
        String valueDeserializer = "com.morningstar.dp.messaging.common.serialization.avro.AvroWithSchemaGenericSerde";
        String serdePojoClass = "com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent";

        config.setTopic(topic);
        config.setClientId(clientId);
        config.setGroupId(groupId);
        config.setKeyDeserializer(keyDeserializer);
        config.setValueDeserializer(valueDeserializer);
        config.setSerdePojoClass(serdePojoClass);

        assertEquals(topic, config.getTopic());
        assertEquals(clientId, config.getClientId());
        assertEquals(groupId, config.getGroupId());
        assertEquals(keyDeserializer, config.getKeyDeserializer());
        assertEquals(valueDeserializer, config.getValueDeserializer());
        assertEquals(serdePojoClass, config.getSerdePojoClass());
    }

    @Test
    @DisplayName("ConsumerEndpointConfig constructor with parameters should work")
    void testConsumerEndpointConfigParameterizedConstructor() {
        String topic = "test-topic";
        String clientId = "test-client";
        String groupId = "test-group";
        String keyDeserializer = "test-key-deserializer";
        String valueDeserializer = "test-value-deserializer";
        String serdePojoClass = "test-serde-class";

        KafkaProperties.ConsumerEndpointConfig config = new KafkaProperties.ConsumerEndpointConfig(
            topic, clientId, groupId, keyDeserializer, valueDeserializer, serdePojoClass
        );

        assertEquals(topic, config.getTopic());
        assertEquals(clientId, config.getClientId());
        assertEquals(groupId, config.getGroupId());
        assertEquals(keyDeserializer, config.getKeyDeserializer());
        assertEquals(valueDeserializer, config.getValueDeserializer());
        assertEquals(serdePojoClass, config.getSerdePojoClass());
    }

    @Test
    @DisplayName("KafkaProperties should handle null and empty values")
    void testKafkaPropertiesNullAndEmptyValues() {
        // Test null values
        kafkaProperties.setRegion(null);
        kafkaProperties.setEnvironment(null);
        kafkaProperties.setClientPrincipal(null);
        kafkaProperties.setKeytabRef(null);
        kafkaProperties.setConsumerEndpoints(null);

        assertNull(kafkaProperties.getRegion());
        assertNull(kafkaProperties.getEnvironment());
        assertNull(kafkaProperties.getClientPrincipal());
        assertNull(kafkaProperties.getKeytabRef());
        assertNull(kafkaProperties.getConsumerEndpoints());

        // Test empty values
        kafkaProperties.setRegion("");
        kafkaProperties.setEnvironment("");
        kafkaProperties.setClientPrincipal("");
        kafkaProperties.setKeytabRef("");
        kafkaProperties.setConsumerEndpoints(new HashMap<>());

        assertEquals("", kafkaProperties.getRegion());
        assertEquals("", kafkaProperties.getEnvironment());
        assertEquals("", kafkaProperties.getClientPrincipal());
        assertEquals("", kafkaProperties.getKeytabRef());
        assertTrue(kafkaProperties.getConsumerEndpoints().isEmpty());
    }

    @Test
    @DisplayName("ConsumerEndpointConfig should handle null and empty values")
    void testConsumerEndpointConfigNullAndEmptyValues() {
        KafkaProperties.ConsumerEndpointConfig config = new KafkaProperties.ConsumerEndpointConfig();

        // Test null values
        config.setTopic(null);
        config.setClientId(null);
        config.setGroupId(null);
        config.setKeyDeserializer(null);
        config.setValueDeserializer(null);
        config.setSerdePojoClass(null);

        assertNull(config.getTopic());
        assertNull(config.getClientId());
        assertNull(config.getGroupId());
        assertNull(config.getKeyDeserializer());
        assertNull(config.getValueDeserializer());
        assertNull(config.getSerdePojoClass());

        // Test empty values
        config.setTopic("");
        config.setClientId("");
        config.setGroupId("");
        config.setKeyDeserializer("");
        config.setValueDeserializer("");
        config.setSerdePojoClass("");

        assertEquals("", config.getTopic());
        assertEquals("", config.getClientId());
        assertEquals("", config.getGroupId());
        assertEquals("", config.getKeyDeserializer());
        assertEquals("", config.getValueDeserializer());
        assertEquals("", config.getSerdePojoClass());
    }

    @Test
    @DisplayName("KafkaProperties toString should work correctly")
    void testKafkaPropertiesToString() {
        kafkaProperties.setRegion("aws-us-east-1");
        kafkaProperties.setEnvironment("devcloud");
        
        String toString = kafkaProperties.toString();
        assertNotNull(toString);
        assertTrue(toString.contains("KafkaProperties"));
        assertTrue(toString.contains("region"));
        assertTrue(toString.contains("environment"));
    }

    @Test
    @DisplayName("ConsumerEndpointConfig toString should work correctly")
    void testConsumerEndpointConfigToString() {
        KafkaProperties.ConsumerEndpointConfig config = new KafkaProperties.ConsumerEndpointConfig();
        config.setTopic("test-topic");
        config.setClientId("test-client");
        
        String toString = config.toString();
        assertNotNull(toString);
        assertTrue(toString.contains("ConsumerEndpointConfig"));
        assertTrue(toString.contains("topic"));
        assertTrue(toString.contains("clientId"));
    }

    @Test
    @DisplayName("KafkaProperties equals and hashCode should work correctly")
    void testKafkaPropertiesEqualsAndHashCode() {
        KafkaProperties props1 = new KafkaProperties();
        KafkaProperties props2 = new KafkaProperties();

        // Test equality with default values
        assertEquals(props1, props2);
        assertEquals(props1.hashCode(), props2.hashCode());

        // Test inequality after modification
        props1.setRegion("aws-us-east-1");
        assertNotEquals(props1, props2);

        // Test equality after same modification
        props2.setRegion("aws-us-east-1");
        assertEquals(props1, props2);
        assertEquals(props1.hashCode(), props2.hashCode());
    }

    @Test
    @DisplayName("ConsumerEndpointConfig equals and hashCode should work correctly")
    void testConsumerEndpointConfigEqualsAndHashCode() {
        KafkaProperties.ConsumerEndpointConfig config1 = new KafkaProperties.ConsumerEndpointConfig();
        KafkaProperties.ConsumerEndpointConfig config2 = new KafkaProperties.ConsumerEndpointConfig();

        // Test equality with default values
        assertEquals(config1, config2);
        assertEquals(config1.hashCode(), config2.hashCode());

        // Test inequality after modification
        config1.setTopic("test-topic");
        assertNotEquals(config1, config2);

        // Test equality after same modification
        config2.setTopic("test-topic");
        assertEquals(config1, config2);
        assertEquals(config1.hashCode(), config2.hashCode());
    }
}
