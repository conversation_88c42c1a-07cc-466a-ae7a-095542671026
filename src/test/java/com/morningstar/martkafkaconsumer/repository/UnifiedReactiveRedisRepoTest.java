package com.morningstar.martkafkaconsumer.repository;


import com.morningstar.martkafkaconsumer.util.NumberUtil;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentMatchers;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.ReactiveHashOperations;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.data.redis.core.ReactiveValueOperations;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.time.Duration;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@ExtendWith(MockitoExtension.class)
public class UnifiedReactiveRedisRepoTest {

    @Mock
    private LettuceConnectionFactory mockFactory;

    @Mock
    private ReactiveRedisTemplate<byte[], byte[]> mockTemplate;

    @Mock
    private ReactiveHashOperations<byte[], byte[], byte[]> mockHashOps;

    @Mock
    private ReactiveValueOperations<byte[], byte[]> mockValueOps;

    private UnifiedReactiveRedisRepo repository;

    @BeforeEach
    void setUp() {
        // Only setup what's needed globally (nothing in this case)
    }

    @Test
    void constructor_WithNullFactories_ShouldThrowException() {
        Assertions.assertThrows(IllegalArgumentException.class, () -> {
            new UnifiedReactiveRedisRepo(null);
        });
    }

    @Test
    void constructor_WithEmptyFactories_ShouldThrowException() {
        Assertions.assertThrows(IllegalArgumentException.class, () -> {
            new UnifiedReactiveRedisRepo(Collections.emptyList());
        });
    }

    @Test
    void constructor_WithValidFactories_ShouldCreateInstance() {
        List<LettuceConnectionFactory> factories = Collections.singletonList(mockFactory);

        Assertions.assertDoesNotThrow(() -> {
            repository = new UnifiedReactiveRedisRepo(factories);
        });
    }

    @Test
    void getHashValueList_WithValidData_ShouldReturnData() {
        // Given
        repository = createMockedRepository();
        Mockito.when(mockTemplate.<byte[], byte[]>opsForHash()).thenReturn(mockHashOps);
        byte[] redisKey = "test-key".getBytes();
        List<byte[]> hashFields = Arrays.asList("field1".getBytes(), "field2".getBytes());

        // Mock Redis response with expire field
        List<byte[]> redisResponse = Arrays.asList(
                "value1".getBytes(),
                "value2".getBytes(),
                NumberUtil.longToBytes(System.currentTimeMillis() + 60000) // Future timestamp
        );

        Mockito.when(mockHashOps.multiGet(Mockito.any(), Mockito.anyList()))
                .thenReturn(Mono.just(redisResponse));

        // When & Then
        StepVerifier.create(repository.getHashValueList(redisKey, hashFields))
                .expectNextMatches(result -> {
                    return result.size() == 2 &&
                            Arrays.equals(result.get(0), "value1".getBytes()) &&
                            Arrays.equals(result.get(1), "value2".getBytes());
                })
                .verifyComplete();
    }

    @Test
    void getHashValueList_WithExpiredData_ShouldReturnEmpty() {
        repository = createMockedRepository();
        Mockito.when(mockTemplate.<byte[], byte[]>opsForHash()).thenReturn(mockHashOps);
        byte[] redisKey = "expired-key".getBytes();
        List<byte[]> hashFields = Collections.singletonList("field1".getBytes());

        // Mock Redis response with expired timestamp
        List<byte[]> expiredResponse = Arrays.asList(
                "value1".getBytes(),
                NumberUtil.longToBytes(System.currentTimeMillis() - 6000) // Past timestamp
        );

        Mockito.when(mockHashOps.multiGet(ArgumentMatchers.any(byte[].class), ArgumentMatchers.any(List.class)))
                .thenReturn(Mono.just(expiredResponse));

        StepVerifier.create(repository.getHashValueList(redisKey, hashFields))
                .verifyComplete();
    }

    @Test
    void getHashValueList_WithEmptyResponse_ShouldReturnEmpty() {
        // Given
        repository = createMockedRepository();
        Mockito.when(mockTemplate.<byte[], byte[]>opsForHash()).thenReturn(mockHashOps);
        byte[] redisKey = "empty-key".getBytes();
        List<byte[]> hashFields = Collections.singletonList("field1".getBytes());

        Mockito.when(mockHashOps.multiGet(ArgumentMatchers.any(byte[].class), ArgumentMatchers.any(List.class)))
                .thenReturn(Mono.just(Collections.emptyList()));

        StepVerifier.create(repository.getHashValueList(redisKey, hashFields))
                .verifyComplete();
    }

    @Test
    void getHashValue_WithValidData_ShouldReturnFilteredData() {
        // Given
        repository = createMockedRepository();
        Mockito.when(mockTemplate.<byte[], byte[]>opsForHash()).thenReturn(mockHashOps);
        byte[] redisKey = "test-key".getBytes();
        List<byte[]> hashFields = Arrays.asList("field1".getBytes(), "field2".getBytes());

        // Mock Redis response
        byte[] expire = NumberUtil.longToBytes(System.currentTimeMillis() + 600000);
        List<byte[]> redisResponse = Arrays.asList(
                "value1".getBytes(),
                new byte[0], // Empty value - should be filtered out
                expire
        );

        Mockito.when(mockHashOps.multiGet(Mockito.any(), Mockito.anyList()))
                .thenReturn(Mono.just(redisResponse));

        // When & Then
        StepVerifier.create(repository.getHashValue(redisKey, hashFields))
                .expectNextMatches(bytes -> Arrays.equals(bytes, "value1".getBytes()))
                .verifyComplete();
    }

    @Test
    void multiGetHashValues_WithValidData_ShouldReturnMaps() {
        // Given
        repository = createMockedRepository();
        Mockito.when(mockTemplate.<byte[], byte[]>opsForHash()).thenReturn(mockHashOps);
        List<byte[]> cacheKeys = Arrays.asList("key1".getBytes(), "key2".getBytes());
        List<byte[]> hashFields = Collections.singletonList("field1".getBytes());

        // Mock responses for both keys
        List<byte[]> response1 = Arrays.asList(
                "value1".getBytes(),
                NumberUtil.longToBytes(System.currentTimeMillis() + 600000)
        );
        List<byte[]> response2 = Arrays.asList(
                "value2".getBytes(),
                NumberUtil.longToBytes(System.currentTimeMillis() + 60000)
        );

        Mockito.when(mockHashOps.multiGet(ArgumentMatchers.eq(cacheKeys.get(0)), ArgumentMatchers.any(List.class)))
                .thenReturn(Mono.just(response1));
        Mockito.when(mockHashOps.multiGet(ArgumentMatchers.eq(cacheKeys.get(1)), ArgumentMatchers.any(List.class)))
                .thenReturn(Mono.just(response2));

        // When & Then
        StepVerifier.create(repository.multiGetHashValues(cacheKeys, hashFields))
                .expectNextCount(2) // Should return 2 maps
                .verifyComplete();
    }

    @Test
    void setHash_WithValidData_ShouldReturnOK() {
        // Given
        repository = createMockedRepository();
        Mockito.when(mockTemplate.<byte[], byte[]>opsForHash()).thenReturn(mockHashOps);
        byte[] key = "test-key".getBytes();
        Map<byte[], byte[]> fieldMap = new HashMap<>();
        fieldMap.put("field1".getBytes(), "value1".getBytes());

        Mockito.when(mockHashOps.putAll(ArgumentMatchers.any(byte[].class), ArgumentMatchers.any(Map.class)))
                .thenReturn(Mono.just(true));
        Mockito.when(mockTemplate.expire(ArgumentMatchers.any(byte[].class), ArgumentMatchers.any(Duration.class)))
                .thenReturn(Mono.just(true));

        // When & Then
        StepVerifier.create(repository.setHash(key, fieldMap, 1L))
                .expectNext(true)
                .verifyComplete();
    }

    @Test
    void setHash_WithEmptyData_ShouldReturnEmpty() {
        // Given
        repository = createMockedRepository();
        byte[] key = "test-key".getBytes();
        Map<byte[], byte[]> emptyMap = Collections.emptyMap();

        // When & Then
        StepVerifier.create(repository.setHash(key, emptyMap, 1L))
                .verifyComplete(); // Should complete without emitting any items
    }

    @Test
    void setHash_WithRedisError_ShouldReturnEmpty() {
        // Given
        repository = createMockedRepository();
        Mockito.when(mockTemplate.<byte[], byte[]>opsForHash()).thenReturn(mockHashOps);
        byte[] key = "test-key".getBytes();
        Map<byte[], byte[]> fieldMap = new HashMap<>();
        fieldMap.put("field1".getBytes(), "value1".getBytes());

        Mockito.when(mockHashOps.putAll(Mockito.any(), Mockito.any()))
                .thenReturn(Mono.error(new RuntimeException("Redis error")));

        // When & Then
        StepVerifier.create(repository.setHash(key, fieldMap, 1L))
                .verifyComplete(); // Should complete empty due to error handling
    }

    @Test
    void getString_WithValidKey_ShouldReturnValue() {
        // Given
        repository = createMockedRepository();
        Mockito.when(mockTemplate.opsForValue()).thenReturn(mockValueOps);
        String key = "test-key";
        byte[] expectedValue = "test-value".getBytes();

        Mockito.when(mockValueOps.get(ArgumentMatchers.any(byte[].class)))
                .thenReturn(Mono.just(expectedValue));

        // When & Then
        StepVerifier.create(repository.getString(key))
                .expectNext("test-value")
                .verifyComplete();
    }

    @Test
    void getString_WithNullKey_ShouldReturnEmpty() {
        // Given
        repository = createMockedRepository();

        // When & Then
        StepVerifier.create(repository.getString(null))
                .verifyComplete();
    }

    @Test
    void getString_WithEmptyKey_ShouldReturnEmpty() {
        // Given
        repository = createMockedRepository();

        // When & Then
        StepVerifier.create(repository.getString(""))
                .verifyComplete();
    }

    @Test
    void setString_WithValidData_ShouldReturnTrue() {
        // Given
        repository = createMockedRepository();
        Mockito.when(mockTemplate.opsForValue()).thenReturn(mockValueOps);
        String key = "test-key";
        String value = "test-value";
        Duration ttl = Duration.ofHours(1);

        Mockito.when(mockValueOps.set(ArgumentMatchers.any(byte[].class),
                        ArgumentMatchers.any(byte[].class),
                        ArgumentMatchers.any(Duration.class)))
                .thenReturn(Mono.just(true));

        // When & Then
        StepVerifier.create(repository.setString(key, value, ttl))
                .expectNext(true)
                .verifyComplete();
    }

    @Test
    void multiSetHash_WithValidData_ShouldReturnResults() {
        // Given
        repository = createMockedRepository();
        Mockito.when(mockTemplate.<byte[], byte[]>opsForHash()).thenReturn(mockHashOps);
        Map<byte[], Map<byte[], byte[]>> data = new HashMap<>();
        Map<byte[], byte[]> fieldMap = new HashMap<>();
        fieldMap.put("field1".getBytes(), "value1".getBytes());
        data.put("key1".getBytes(), fieldMap);

        Mockito.when(mockHashOps.putAll(ArgumentMatchers.any(byte[].class), ArgumentMatchers.any(Map.class)))
                .thenReturn(Mono.just(true));
        Mockito.when(mockTemplate.expire(ArgumentMatchers.any(byte[].class), ArgumentMatchers.any(Duration.class)))
                .thenReturn(Mono.just(true));

        // When & Then
        StepVerifier.create(repository.multiSetHash(data, 1L))
                .expectNext(true)
                .verifyComplete();
    }

    @Test
    void destroy_ShouldCallDestroyOnFactories() {
        // Given
        List<LettuceConnectionFactory> factories = Collections.singletonList(mockFactory);
        repository = new UnifiedReactiveRedisRepo(factories);

        // When
        Assertions.assertDoesNotThrow(() -> repository.destroy());

        // Then
        Mockito.verify(mockFactory, Mockito.times(1)).destroy();
    }

    @Test
    void destroy_WithExceptionInFactory_ShouldNotThrow() {
        // Given
        List<LettuceConnectionFactory> factories = Collections.singletonList(mockFactory);
        repository = new UnifiedReactiveRedisRepo(factories);

        Mockito.doThrow(new RuntimeException("Destroy error")).when(mockFactory).destroy();

        // When & Then
        Assertions.assertDoesNotThrow(() -> repository.destroy());
    }

    /**
     * Helper method to create a repository with mocked template
     */
    private UnifiedReactiveRedisRepo createMockedRepository() {
        return new UnifiedReactiveRedisRepo(Collections.singletonList(mockFactory)) {
            @Override
            protected List<ReactiveRedisTemplate<byte[], byte[]>> createRedisTemplates(List<LettuceConnectionFactory> factories) {
                return Collections.singletonList(mockTemplate);
            }
        };
    }
}
