package com.morningstar.martkafkaconsumer.repository;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockedConstruction;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import reactor.core.publisher.Flux;
import reactor.test.StepVerifier;

@RunWith(MockitoJUnitRunner.class)
public class RedisTsRepoTest {
    private RedisTsRepo redisTsRepo;

    @Mock
    private LettuceConnectionFactory connectionFactory;

    @Mock
    private UnifiedReactiveRedisRepo mockUnifiedReactiveRedisRepo;

    @Before
    public void setUp() {
        // Mock the UnifiedRedisRepo construction to return our mock
        try (MockedConstruction<UnifiedReactiveRedisRepo> mockedConstruction = Mockito.mockConstruction(UnifiedReactiveRedisRepo.class,
                (mock, context) -> {
                    // Configure the mock to behave like our mockUnifiedRedisRepo
                    when(mock.getHashValue(any(), any())).thenAnswer(invocation ->
                            mockUnifiedReactiveRedisRepo.getHashValue(invocation.getArgument(0), invocation.getArgument(1)));
                    when(mock.setHash(any(), any(), any(Long.class))).thenAnswer(invocation ->
                            mockUnifiedReactiveRedisRepo.setHash(invocation.getArgument(0), invocation.getArgument(1), invocation.getArgument(2)));
                    when(mock.multiSetHash(any(), any(Long.class))).thenAnswer(invocation ->
                            mockUnifiedReactiveRedisRepo.multiSetHash(invocation.getArgument(0), invocation.getArgument(1)));
                })) {

            redisTsRepo = new RedisTsRepo(List.of(connectionFactory));
        }
    }

    @Test
    public void testGetHashValue() {
        byte[] redisKey = "testKey".getBytes();
        List<byte[]> hashFields = List.of("field1".getBytes(), "field2".getBytes());
        byte[] value1 = "value1".getBytes();
        byte[] value2 = "value2".getBytes();

        // Mock the UnifiedRedisRepo behavior
        when(mockUnifiedReactiveRedisRepo.getHashValue(any(), any()))
                .thenReturn(Flux.just(value1, value2));

        Flux<byte[]> result = redisTsRepo.getHashValue(redisKey, hashFields);

        StepVerifier.create(result)
                .expectNext(value1, value2)
                .verifyComplete();
    }

    @Test
    public void testSetHash_Success() {
        Map<byte[], byte[]> data = new HashMap<>();
        data.put("field1".getBytes(), "value1".getBytes());
        byte[] key = "testKey".getBytes();
        long expireHours = 1L;

        // Mock the UnifiedRedisRepo behavior
        when(mockUnifiedReactiveRedisRepo.setHash(any(), any(), any(Long.class)))
                .thenReturn(Flux.just(true));

        Flux<Boolean> result = redisTsRepo.setHash(key, data, expireHours);

        StepVerifier.create(result)
                .expectNext(true)
                .verifyComplete();
    }

    @Test
    public void testSetHash_Empty() {
        Map<byte[], byte[]> data = new HashMap<>();
        byte[] key = "testKey".getBytes();
        long expireHours = 1L;

        // Mock the UnifiedRedisRepo behavior for empty data
        when(mockUnifiedReactiveRedisRepo.setHash(any(), any(), any(Long.class)))
                .thenReturn(Flux.empty());

        Flux<Boolean> result = redisTsRepo.setHash(key, data, expireHours);

        StepVerifier.create(result)
                .expectComplete()
                .verify();
    }

    @Test
    public void testMultiSetHash() {
        byte[] key = "testKey".getBytes();
        Map<byte[], byte[]> data = new HashMap<>();
        data.put("field1".getBytes(), "value1".getBytes());
        Map<byte[], Map<byte[], byte[]>> map = new HashMap<>();
        map.put(key, data);
        long expireHours = 1L;

        // Mock the UnifiedRedisRepo behavior
        when(mockUnifiedReactiveRedisRepo.multiSetHash(any(), any(Long.class)))
                .thenReturn(Flux.just("OK"));

        Flux<Object> result = redisTsRepo.multiSetHash(map, expireHours);

        StepVerifier.create(result)
                .expectNext("OK")
                .verifyComplete();
    }

    @Test
    public void testDestroy() {
        // Test that destroy method doesn't throw exceptions
        redisTsRepo.destroy();

        // Verify that destroy was called (this would be verified through logging in real scenario)
        // Since we're mocking the construction, we can't easily verify the destroy call
        // but we can ensure no exceptions are thrown
    }
}