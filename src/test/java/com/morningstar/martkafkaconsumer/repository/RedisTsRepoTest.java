package com.morningstar.martkafkaconsumer.repository;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockedConstruction;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

@RunWith(MockitoJUnitRunner.class)
public class RedisTsRepoTest {
    private RedisTsRepo redisTsRepo;

    @Mock
    private LettuceConnectionFactory connectionFactory;

    @Mock
    private UnifiedReactiveRedisRepo mockUnifiedReactiveRedisRepo;

    @Before
    public void setUp() {
        // Mock the UnifiedRedisRepo construction to return our mock
        try (MockedConstruction<UnifiedReactiveRedisRepo> mockedConstruction = Mockito.mockConstruction(UnifiedReactiveRedisRepo.class,
                (mock, context) -> {
                    // Configure the mock to behave like our mockUnifiedRedisRepo
                    when(mock.getHashValue(any(), any())).thenAnswer(invocation ->
                            mockUnifiedReactiveRedisRepo.getHashValue(invocation.getArgument(0), invocation.getArgument(1)));
                    when(mock.setHash(any(), any(), any(Long.class))).thenAnswer(invocation ->
                            mockUnifiedReactiveRedisRepo.setHash(invocation.getArgument(0), invocation.getArgument(1), invocation.getArgument(2)));
                    when(mock.multiSetHash(any(), any(Long.class))).thenAnswer(invocation ->
                            mockUnifiedReactiveRedisRepo.multiSetHash(invocation.getArgument(0), invocation.getArgument(1)));
                    when(mock.getHashValueList(any(), any())).thenAnswer(invocation ->
                            mockUnifiedReactiveRedisRepo.getHashValueList(invocation.getArgument(0), invocation.getArgument(1)));
                    when(mock.setHashWithoutExpiration(any(), any())).thenAnswer(invocation ->
                            mockUnifiedReactiveRedisRepo.setHashWithoutExpiration(invocation.getArgument(0), invocation.getArgument(1)));
                })) {

            redisTsRepo = new RedisTsRepo(List.of(connectionFactory));
        }
    }

    @Test
    public void testGetHashValue() {
        byte[] redisKey = "testKey".getBytes();
        List<byte[]> hashFields = List.of("field1".getBytes(), "field2".getBytes());
        byte[] value1 = "value1".getBytes();
        byte[] value2 = "value2".getBytes();

        // Mock the UnifiedRedisRepo behavior
        when(mockUnifiedReactiveRedisRepo.getHashValue(any(), any()))
                .thenReturn(Flux.just(value1, value2));

        Flux<byte[]> result = redisTsRepo.getHashValue(redisKey, hashFields);

        StepVerifier.create(result)
                .expectNext(value1, value2)
                .verifyComplete();
    }

    @Test
    public void testGetMultipleHashValues_Success() {
        byte[] redisKey = "testKey".getBytes();
        List<byte[]> hashFields = List.of("field1".getBytes(), "field2".getBytes());
        List<byte[]> values = List.of("value1".getBytes(), "value2".getBytes());

        // Mock the UnifiedRedisRepo behavior
        when(mockUnifiedReactiveRedisRepo.getHashValueList(any(), any()))
                .thenReturn(Mono.just(values));

        Map<byte[], byte[]> result = redisTsRepo.getMultipleHashValues(redisKey, hashFields);

        assertEquals(2, result.size());
        assertTrue(result.containsKey(hashFields.get(0)));
        assertTrue(result.containsKey(hashFields.get(1)));
        assertEquals("value1", new String(result.get(hashFields.get(0))));
        assertEquals("value2", new String(result.get(hashFields.get(1))));
    }

    @Test
    public void testGetMultipleHashValues_WithNullValues() {
        byte[] redisKey = "testKey".getBytes();
        List<byte[]> hashFields = List.of("field1".getBytes(), "field2".getBytes(), "field3".getBytes());
        List<byte[]> values = new ArrayList<>();
        values.add("value1".getBytes());
        values.add(null);
        values.add("value3".getBytes());

        // Mock the UnifiedRedisRepo behavior
        when(mockUnifiedReactiveRedisRepo.getHashValueList(any(), any()))
                .thenReturn(Mono.just(values));

        Map<byte[], byte[]> result = redisTsRepo.getMultipleHashValues(redisKey, hashFields);

        assertEquals(2, result.size()); // Only non-null values should be included
        assertTrue(result.containsKey(hashFields.get(0)));
        assertTrue(result.containsKey(hashFields.get(2)));
        assertEquals("value1", new String(result.get(hashFields.get(0))));
        assertEquals("value3", new String(result.get(hashFields.get(2))));
    }

    @Test
    public void testGetMultipleHashValues_WithEmptyValues() {
        byte[] redisKey = "testKey".getBytes();
        List<byte[]> hashFields = List.of("field1".getBytes(), "field2".getBytes());
        List<byte[]> values = new ArrayList<>();
        values.add("value1".getBytes());
        values.add(new byte[0]); // Empty byte array

        // Mock the UnifiedRedisRepo behavior
        when(mockUnifiedReactiveRedisRepo.getHashValueList(any(), any()))
                .thenReturn(Mono.just(values));

        Map<byte[], byte[]> result = redisTsRepo.getMultipleHashValues(redisKey, hashFields);

        assertEquals(1, result.size()); // Only non-empty values should be included
        assertTrue(result.containsKey(hashFields.get(0)));
        assertEquals("value1", new String(result.get(hashFields.get(0))));
    }

    @Test
    public void testGetMultipleHashValues_EmptyResult() {
        byte[] redisKey = "testKey".getBytes();
        List<byte[]> hashFields = List.of("field1".getBytes(), "field2".getBytes());

        // Mock the UnifiedRedisRepo behavior to return empty
        when(mockUnifiedReactiveRedisRepo.getHashValueList(any(), any()))
                .thenReturn(Mono.empty());

        Map<byte[], byte[]> result = redisTsRepo.getMultipleHashValues(redisKey, hashFields);

        assertTrue(result.isEmpty());
    }

    @Test
    public void testSetHash_Success() {
        Map<byte[], byte[]> data = new HashMap<>();
        data.put("field1".getBytes(), "value1".getBytes());
        byte[] key = "testKey".getBytes();
        long expireHours = 1L;

        // Mock the UnifiedRedisRepo behavior
        when(mockUnifiedReactiveRedisRepo.setHash(any(), any(), any(Long.class)))
                .thenReturn(Flux.just(true));

        Flux<Boolean> result = redisTsRepo.setHash(key, data, expireHours);

        StepVerifier.create(result)
                .expectNext(true)
                .verifyComplete();
    }

    @Test
    public void testSetHash_Empty() {
        Map<byte[], byte[]> data = new HashMap<>();
        byte[] key = "testKey".getBytes();
        long expireHours = 1L;

        // Mock the UnifiedRedisRepo behavior for empty data
        when(mockUnifiedReactiveRedisRepo.setHash(any(), any(), any(Long.class)))
                .thenReturn(Flux.empty());

        Flux<Boolean> result = redisTsRepo.setHash(key, data, expireHours);

        StepVerifier.create(result)
                .expectComplete()
                .verify();
    }

    @Test
    public void testMultiSetHash() {
        byte[] key = "testKey".getBytes();
        Map<byte[], byte[]> data = new HashMap<>();
        data.put("field1".getBytes(), "value1".getBytes());
        Map<byte[], Map<byte[], byte[]>> map = new HashMap<>();
        map.put(key, data);
        long expireHours = 1L;

        // Mock the UnifiedRedisRepo behavior
        when(mockUnifiedReactiveRedisRepo.multiSetHash(any(), any(Long.class)))
                .thenReturn(Flux.just("OK"));

        Flux<Object> result = redisTsRepo.multiSetHash(map, expireHours);

        StepVerifier.create(result)
                .expectNext("OK")
                .verifyComplete();
    }

    @Test
    public void testSetHashWithoutExpiration_Success() {
        Map<byte[], byte[]> data = new HashMap<>();
        data.put("field1".getBytes(), "value1".getBytes());
        data.put("field2".getBytes(), "value2".getBytes());
        byte[] key = "testKey".getBytes();

        // Mock the UnifiedRedisRepo behavior
        when(mockUnifiedReactiveRedisRepo.setHashWithoutExpiration(any(), any()))
                .thenReturn(Flux.just(true));

        Flux<Boolean> result = redisTsRepo.setHashWithoutExpiration(key, data);

        StepVerifier.create(result)
                .expectNext(true)
                .verifyComplete();
    }

    @Test
    public void testSetHashWithoutExpiration_EmptyData() {
        Map<byte[], byte[]> data = new HashMap<>();
        byte[] key = "testKey".getBytes();

        // Mock the UnifiedRedisRepo behavior for empty data
        when(mockUnifiedReactiveRedisRepo.setHashWithoutExpiration(any(), any()))
                .thenReturn(Flux.empty());

        Flux<Boolean> result = redisTsRepo.setHashWithoutExpiration(key, data);

        StepVerifier.create(result)
                .expectComplete()
                .verify();
    }

    @Test
    public void testSetHashWithoutExpiration_Error() {
        Map<byte[], byte[]> data = new HashMap<>();
        data.put("field1".getBytes(), "value1".getBytes());
        byte[] key = "testKey".getBytes();

        // Mock the UnifiedRedisRepo behavior to return error
        when(mockUnifiedReactiveRedisRepo.setHashWithoutExpiration(any(), any()))
                .thenReturn(Flux.error(new RuntimeException("Redis error")));

        Flux<Boolean> result = redisTsRepo.setHashWithoutExpiration(key, data);

        StepVerifier.create(result)
                .expectError(RuntimeException.class)
                .verify();
    }

    @Test
    public void testDestroy() {
        // Test that destroy method doesn't throw exceptions
        redisTsRepo.destroy();

        // Verify that destroy was called (this would be verified through logging in real scenario)
        // Since we're mocking the construction, we can't easily verify the destroy call
        // but we can ensure no exceptions are thrown
    }
}