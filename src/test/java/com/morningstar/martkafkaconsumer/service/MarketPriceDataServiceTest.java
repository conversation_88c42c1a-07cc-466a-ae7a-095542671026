package com.morningstar.martkafkaconsumer.service;

import com.morningstar.data.domain.ics.eod.enriched.CopyOverReasonEnum;
import com.morningstar.data.domain.ics.eod.enriched.MarketPriceDetail;
import com.morningstar.data.domain.proto.TsDataProtoBuf;
import com.morningstar.martkafkaconsumer.config.MarketPriceDataConfig;
import com.morningstar.martkafkaconsumer.repository.RedisTsRepo;
import com.morningstar.martkafkaconsumer.util.Lz4Util;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.lenient;

@ExtendWith(MockitoExtension.class)
class MarketPriceDataServiceTest {

    @Mock
    private RedisTsRepo redisTsRepo;

    @Mock
    private MarketPriceDataConfig.MarketPriceData config;

    private MarketPriceDataService marketPriceDataService;

    @BeforeEach
    void setUp() {
        lenient().when(config.getRedisKeyPrefix()).thenReturn("ts:MarketPrice:");
        lenient().when(config.getYearsPerKey()).thenReturn(10);
        lenient().when(config.getYearsPerField()).thenReturn(2);
        lenient().when(config.getDefaultExpirationHours()).thenReturn(24L);
        lenient().when(config.getDataPointMapping()).thenReturn(Map.of(
            "high", "EO001",
            "low", "EO002",
            "open", "EO003",
            "close", "EO004",
            "tradedVolume", "EO005",
            "copyOverReason", "EO006"
        ));

        lenient().when(redisTsRepo.getHashValue(any(byte[].class), anyList())).thenReturn(Flux.empty());
        lenient().when(redisTsRepo.getMultipleHashValues(any(byte[].class), anyList())).thenReturn(new HashMap<>());
        lenient().when(redisTsRepo.setHashWithoutExpiration(any(byte[].class), anyMap())).thenReturn(Flux.just(true));

        marketPriceDataService = new MarketPriceDataService(redisTsRepo, config);
    }

    @Test
    void testStoreMarketPriceDetail_ValidData() {
        MarketPriceDetail marketPriceDetail = createTestMarketPriceDetail();

        assertDoesNotThrow(() -> {
            marketPriceDataService.storeMarketPriceDetail(marketPriceDetail);
        });

        verify(redisTsRepo, times(1)).getMultipleHashValues(any(byte[].class), anyList());
        verify(redisTsRepo, times(1)).setHashWithoutExpiration(any(byte[].class), anyMap());
    }

    @Test
    void testStoreMarketPriceDetail_NullInput() {

        verify(redisTsRepo, never()).getMultipleHashValues(any(byte[].class), anyList());
        verify(redisTsRepo, never()).setHash(any(byte[].class), anyMap(), anyLong());
    }

    @Test
    void testStoreMarketPriceDetail_MissingInvestmentId() {
        MarketPriceDetail marketPriceDetail = createTestMarketPriceDetail();
        marketPriceDetail.setPerformanceId(null);

        assertDoesNotThrow(() -> {
            marketPriceDataService.storeMarketPriceDetail(marketPriceDetail);
        });

        verify(redisTsRepo, never()).getMultipleHashValues(any(byte[].class), anyList());
        verify(redisTsRepo, never()).setHashWithoutExpiration(any(byte[].class), anyMap());
    }

    @Test
    void testStoreMarketPriceDetail_MissingDate() {
        // Arrange
        MarketPriceDetail marketPriceDetail = createTestMarketPriceDetail();
        marketPriceDetail.setDate(null);

        // Act & Assert
        assertDoesNotThrow(() -> {
            marketPriceDataService.storeMarketPriceDetail(marketPriceDetail);
        });

        // Verify no Redis operations were called
        verify(redisTsRepo, never()).getHashValue(any(byte[].class), anyList());
        verify(redisTsRepo, never()).setHashWithoutExpiration(any(byte[].class), anyMap());
    }

    @Test
    void testRedisKeyGeneration() {
        // Test key generation for different years
        LocalDate date2020 = LocalDate.of(2020, 5, 15);
        LocalDate date2025 = LocalDate.of(2025, 8, 20);
        LocalDate date1975 = LocalDate.of(1975, 12, 31);

        MarketPriceDetail detail2020 = createTestMarketPriceDetail();
        detail2020.setDate(date2020);
        detail2020.setPerformanceId("TEST001");

        MarketPriceDetail detail2025 = createTestMarketPriceDetail();
        detail2025.setDate(date2025);
        detail2025.setPerformanceId("TEST001");

        MarketPriceDetail detail1975 = createTestMarketPriceDetail();
        detail1975.setDate(date1975);
        detail1975.setPerformanceId("TEST001");

        // Store all details
        marketPriceDataService.storeMarketPriceDetail(detail2020);
        marketPriceDataService.storeMarketPriceDetail(detail2025);
        marketPriceDataService.storeMarketPriceDetail(detail1975);

        // Verify Redis operations were called for each detail (1 batch call per detail * 3 details = 3)
        verify(redisTsRepo, times(3)).setHashWithoutExpiration(any(byte[].class), anyMap());
    }

    @Test
    void testCopyOverReasonHandling() {
        // Test different CopyOverReason values
        MarketPriceDetail detail1 = createTestMarketPriceDetail();
        detail1.setCopyOverReason(CopyOverReasonEnum.PRICE);

        MarketPriceDetail detail2 = createTestMarketPriceDetail();
        detail2.setCopyOverReason(null);

        // Should not throw exceptions
        assertDoesNotThrow(() -> {
            marketPriceDataService.storeMarketPriceDetail(detail1);
            marketPriceDataService.storeMarketPriceDetail(detail2);
        });

        verify(redisTsRepo, times(2)).setHashWithoutExpiration(any(byte[].class), anyMap());
    }

    @Test
    void testNullBigDecimalHandling() {
        // Test with null BigDecimal values
        MarketPriceDetail detail = createTestMarketPriceDetail();
        detail.setHigh(null);
        detail.setLow(null);
        detail.setOpen(null);
        detail.setClose(null);
        detail.setTradedVolume(null);

        // Should not throw exceptions
        assertDoesNotThrow(() -> {
            marketPriceDataService.storeMarketPriceDetail(detail);
        });

        verify(redisTsRepo, times(1)).setHashWithoutExpiration(any(byte[].class), anyMap());
    }

    @Test
    void testRedisOperationFailure() {
        // Arrange - Mock Redis to throw exception
        when(redisTsRepo.setHashWithoutExpiration(any(byte[].class), anyMap()))
            .thenReturn(Flux.error(new RuntimeException("Redis connection failed")));

        MarketPriceDetail marketPriceDetail = createTestMarketPriceDetail();

        // Act & Assert
        assertThrows(RuntimeException.class, () -> {
            marketPriceDataService.storeMarketPriceDetail(marketPriceDetail);
        });
    }

    @Test
    void testStoreMarketPriceDetail_BatchReadOptimization() {
        MarketPriceDetail marketPriceDetail = createTestMarketPriceDetail();

        Map<byte[], byte[]> mockExistingData = new HashMap<>();
        when(redisTsRepo.getMultipleHashValues(any(byte[].class), anyList())).thenReturn(mockExistingData);

        assertDoesNotThrow(() -> {
            marketPriceDataService.storeMarketPriceDetail(marketPriceDetail);
        });

        verify(redisTsRepo, times(1)).getMultipleHashValues(any(byte[].class), argThat(fields ->
            fields != null && fields.size() == 5));

        verify(redisTsRepo, times(1)).setHashWithoutExpiration(any(byte[].class), anyMap());

        verify(redisTsRepo, never()).getHashValue(any(byte[].class), anyList());
    }



    @Test
    void test_mergeData_with_existing_date_update() {
        // Test mergeData method with existing date (update scenario)
        MarketPriceDetail detail = createTestMarketPriceDetail();
        detail.setCopyOverReason(CopyOverReasonEnum.PRICE); // Use PRICE reason

        // Mock existing data with the same date
        byte[] existingData = createMockExistingTsData();
        Map<byte[], byte[]> mockRedisData = new HashMap<>();
        mockRedisData.put("EO001:2020".getBytes(), existingData);
        mockRedisData.put("EO002:2020".getBytes(), existingData);
        mockRedisData.put("EO003:2020".getBytes(), existingData);
        mockRedisData.put("EO004:2020".getBytes(), existingData);
        mockRedisData.put("EO005:2020".getBytes(), existingData);

        when(redisTsRepo.getMultipleHashValues(any(byte[].class), anyList())).thenReturn(mockRedisData);

        assertDoesNotThrow(() -> marketPriceDataService.storeMarketPriceDetail(detail));

        verify(redisTsRepo, times(1)).setHashWithoutExpiration(any(byte[].class), anyMap());
    }

    @Test
    void test_mergeData_with_new_date_insertion() {
        // Test mergeData method with new date (insertion scenario)
        MarketPriceDetail detail = createTestMarketPriceDetail();
        detail.setDate(LocalDate.of(2023, 7, 20)); // Different date
        detail.setCopyOverReason(null); // Test null copyover reason to trigger non-PRICE branch

        // Mock existing data with different dates
        byte[] existingData = createMockExistingTsDataWithDifferentDates();
        Map<byte[], byte[]> mockRedisData = new HashMap<>();
        mockRedisData.put("EO001:2020".getBytes(), existingData);
        mockRedisData.put("EO002:2020".getBytes(), existingData);
        mockRedisData.put("EO003:2020".getBytes(), existingData);
        mockRedisData.put("EO004:2020".getBytes(), existingData);
        mockRedisData.put("EO005:2020".getBytes(), existingData);

        when(redisTsRepo.getMultipleHashValues(any(byte[].class), anyList())).thenReturn(mockRedisData);

        assertDoesNotThrow(() -> marketPriceDataService.storeMarketPriceDetail(detail));

        verify(redisTsRepo, times(1)).setHashWithoutExpiration(any(byte[].class), anyMap());
    }

    @Test
    void test_mergeData_with_copyover_reason_update() {
        // Test mergeData with existing copyover date update scenario
        MarketPriceDetail detail = createTestMarketPriceDetail();
        detail.setCopyOverReason(null); // Test null copyover reason

        // Mock existing data with same date and existing copyover data
        byte[] existingData = createMockExistingTsDataWithCopyOver();
        Map<byte[], byte[]> mockRedisData = new HashMap<>();
        mockRedisData.put("EO001:2020".getBytes(), existingData);
        mockRedisData.put("EO002:2020".getBytes(), existingData);
        mockRedisData.put("EO003:2020".getBytes(), existingData);
        mockRedisData.put("EO004:2020".getBytes(), existingData);
        mockRedisData.put("EO005:2020".getBytes(), existingData);

        when(redisTsRepo.getMultipleHashValues(any(byte[].class), anyList())).thenReturn(mockRedisData);

        assertDoesNotThrow(() -> marketPriceDataService.storeMarketPriceDetail(detail));

        verify(redisTsRepo, times(1)).setHashWithoutExpiration(any(byte[].class), anyMap());
    }

    @Test
    void test_getMultipleExistingData_with_mixed_data() {
        // Test getMultipleExistingData with mixed valid and empty data
        MarketPriceDetail detail = createTestMarketPriceDetail();

        // Mock Redis to return mixed data - some valid, some empty
        Map<byte[], byte[]> mockRedisData = new HashMap<>();
        byte[] validData = createMockExistingTsData();
        mockRedisData.put("EO001:2020".getBytes(), validData);
        mockRedisData.put("EO002:2020".getBytes(), new byte[0]); // Empty data
        // EO003, EO004, EO005 not in map (null data)

        when(redisTsRepo.getMultipleHashValues(any(byte[].class), anyList())).thenReturn(mockRedisData);

        assertDoesNotThrow(() -> marketPriceDataService.storeMarketPriceDetail(detail));

        verify(redisTsRepo, times(1)).setHashWithoutExpiration(any(byte[].class), anyMap());
    }

    @Test
    void test_getMultipleExistingData_with_empty_data() {
        // Test getMultipleExistingData with empty/null data to create new TSData
        MarketPriceDetail detail = createTestMarketPriceDetail();

        // Mock Redis to return empty data for some fields
        Map<byte[], byte[]> mockRedisData = new HashMap<>();
        mockRedisData.put("EO001:2020".getBytes(), new byte[0]); // Empty data
        mockRedisData.put("EO002:2020".getBytes(), null); // Null data
        // Other fields not present in map

        when(redisTsRepo.getMultipleHashValues(any(byte[].class), anyList())).thenReturn(mockRedisData);

        assertDoesNotThrow(() -> marketPriceDataService.storeMarketPriceDetail(detail));

        verify(redisTsRepo, times(1)).setHashWithoutExpiration(any(byte[].class), anyMap());
    }

    @Test
    void test_findInsertPosition_edge_cases() {
        // Test findInsertPosition method indirectly through mergeData
        MarketPriceDetail detail1 = createTestMarketPriceDetail();
        detail1.setDate(LocalDate.of(2023, 1, 1)); // Early date

        MarketPriceDetail detail2 = createTestMarketPriceDetail();
        detail2.setDate(LocalDate.of(2023, 12, 31)); // Late date

        MarketPriceDetail detail3 = createTestMarketPriceDetail();
        detail3.setDate(LocalDate.of(2023, 6, 15)); // Middle date

        // Mock existing data with dates to test binary search
        byte[] existingData = createMockExistingTsDataWithMultipleDates();
        Map<byte[], byte[]> mockRedisData = new HashMap<>();
        mockRedisData.put("EO001:2020".getBytes(), existingData);
        mockRedisData.put("EO002:2020".getBytes(), existingData);
        mockRedisData.put("EO003:2020".getBytes(), existingData);
        mockRedisData.put("EO004:2020".getBytes(), existingData);
        mockRedisData.put("EO005:2020".getBytes(), existingData);

        when(redisTsRepo.getMultipleHashValues(any(byte[].class), anyList())).thenReturn(mockRedisData);

        // Test insertion at beginning, middle, and end
        assertDoesNotThrow(() -> {
            marketPriceDataService.storeMarketPriceDetail(detail1);
            marketPriceDataService.storeMarketPriceDetail(detail2);
            marketPriceDataService.storeMarketPriceDetail(detail3);
        });

        verify(redisTsRepo, times(3)).setHashWithoutExpiration(any(byte[].class), anyMap());
    }

    @Test
    void test_getCopyOverReasonValue_with_null() {
        // Test getCopyOverReasonValue with null input
        MarketPriceDetail detail = createTestMarketPriceDetail();
        detail.setCopyOverReason(null);

        assertDoesNotThrow(() -> marketPriceDataService.storeMarketPriceDetail(detail));

        verify(redisTsRepo, times(1)).setHashWithoutExpiration(any(byte[].class), anyMap());
    }

    private MarketPriceDetail createTestMarketPriceDetail() {
        MarketPriceDetail detail = new MarketPriceDetail();
        detail.setPerformanceId("TEST_PERF_001");
        detail.setHigh(BigDecimal.valueOf(105.50));
        detail.setLow(BigDecimal.valueOf(98.25));
        detail.setOpen(BigDecimal.valueOf(100.00));
        detail.setClose(BigDecimal.valueOf(103.75));
        detail.setTradedVolume(BigDecimal.valueOf(1500000));
        detail.setDate(LocalDate.of(2023, 6, 15));
        detail.setCopyOverReason(CopyOverReasonEnum.PRICE);
        return detail;
    }

    private byte[] createMockExistingTsData() {
        // Create TSData with same date as test data (2023-06-15 = epoch day 19358)
        TsDataProtoBuf.TSDataDouble tsData = TsDataProtoBuf.TSDataDouble.newBuilder()
            .setInvestmentId("TEST_PERF_001")
            .setDpId("EO001")
            .addDates(19358L) // 2023-06-15
            .addValues(100.0)
            .addCopyOverDates(19358L)
            .addCopyOverReasons(0) // PRICE
            .build();

        byte[] serialized = tsData.toByteArray();
        return Lz4Util.compress(serialized);
    }

    private byte[] createMockExistingTsDataWithDifferentDates() {
        // Create TSData with different dates to test insertion
        TsDataProtoBuf.TSDataDouble tsData = TsDataProtoBuf.TSDataDouble.newBuilder()
            .setInvestmentId("TEST_PERF_001")
            .setDpId("EO001")
            .addDates(19350L) // Earlier date
            .addDates(19370L) // Later date
            .addValues(95.0)
            .addValues(110.0)
            .build();

        byte[] serialized = tsData.toByteArray();
        return Lz4Util.compress(serialized);
    }

    private byte[] createMockExistingTsDataWithCopyOver() {
        // Create TSData with existing copyover data for same date
        TsDataProtoBuf.TSDataDouble tsData = TsDataProtoBuf.TSDataDouble.newBuilder()
            .setInvestmentId("TEST_PERF_001")
            .setDpId("EO001")
            .addDates(19358L) // 2023-06-15
            .addValues(100.0)
            .addCopyOverDates(19358L) // Same date already has copyover
            .addCopyOverReasons(1) // Some non-PRICE reason (ordinal 1)
            .build();

        byte[] serialized = tsData.toByteArray();
        return Lz4Util.compress(serialized);
    }

    private byte[] createMockExistingTsDataWithMultipleDates() {
        // Create TSData with multiple dates to test binary search in findInsertPosition
        TsDataProtoBuf.TSDataDouble tsData = TsDataProtoBuf.TSDataDouble.newBuilder()
            .setInvestmentId("TEST_PERF_001")
            .setDpId("EO001")
            .addDates(19300L) // Very early date
            .addDates(19350L) // Early date
            .addDates(19400L) // Late date
            .addDates(19450L) // Very late date
            .addValues(90.0)
            .addValues(95.0)
            .addValues(105.0)
            .addValues(110.0)
            .build();

        byte[] serialized = tsData.toByteArray();
        return Lz4Util.compress(serialized);
    }
}
