kafka:
  region: aws-us-east-1
  environment: dev
  client-principal: <EMAIL>
  keytab-ref: /market-kafka-consumer-non-prod/keytab
  active-consume: marketPriceEnrichedConsumer

  consumer-endpoints:
    market_price_list_updated:
      topic: market_price_list_updated
      clientId: DPDA_Market_Price_Consumer
      groupId: dpda_market_price_group
      keyDeserializer: org.apache.kafka.common.serialization.LongDeserializer
      valueDeserializer: com.morningstar.dp.messaging.common.serialization.avro.AvroWithSchemaGenericSerde
      serdePojoClass: com.morningstar.data.domain.eod.events.MarketPriceEvent
    market_price_enriched_event:
      topic: market_price_enriched_event
      clientId: DPDA_Market_Price_Consumer
      groupId: dpda_market_price_group
      keyDeserializer: org.apache.kafka.common.serialization.LongDeserializer
      valueDeserializer: com.morningstar.dp.messaging.common.serialization.avro.AvroWithSchemaGenericSerde
      serdePojoClass: com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent

redis:
  ts-data:
    host: ts-data-v8-cluster-stg.bg4uxb.clustercfg.use1.cache.amazonaws.com
    port: 6379
  client-name:

market-price:
  raw-price-data:
    redis-key-prefix: "ts:MarketPrice:"
    years-per-key: 20
    years-per-field: 10
    data-point-mapping:
      high: "EO001"
      low: "EO002"
      open: "EO003"
      close: "EO004"
      tradedVolume: "EO005"
  adjusted-price-data:
    redis-key-prefix: "ts:MarketPriceAdjusted:"
    years-per-key: 20
    years-per-field: 10
    data-point-mapping:
      high: "Y2J44"
      low: "OEFC5"
      open: "LN98K"
      close: "KXMBH"
      tradedVolume: "EO006"

executor:
  thread-count: 40

spring:
  application:
    name: mart-kafka-consumer
  profiles:
    active: dev


---
# dev env
spring:
  config:
    activate:
      on-profile: dev
kafka:
  environment: devcloud
#  consumer-endpoints:
#    market_price_enriched_event:
#      topic: 04_13_12_MyNewWorkflowName4
#      clientId: DPDA_Market_Data_Service
#      groupId: dpda_market_price_group1
redis:
  ts-data:
    host: kafka-test.bg4uxb.clustercfg.use1.cache.amazonaws.com

---
# staging env
spring:
  config:
    activate:
      on-profile: stg

kafka:
  environment: staging


---
# prod env
spring:
  config:
    activate:
      on-profile: prod

kafka:
  environment: prod

redis:
  ts-data:
    host:
    port: 6379
  client-name: