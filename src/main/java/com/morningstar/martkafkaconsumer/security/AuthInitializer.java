package com.morningstar.martkafkaconsumer.security;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ClassPathResource;
import software.amazon.awssdk.services.ssm.SsmClient;
import software.amazon.awssdk.services.ssm.model.GetParameterRequest;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class AuthInitializer implements ApplicationContextInitializer<ConfigurableApplicationContext> {
    @Override
    public void initialize(ConfigurableApplicationContext applicationContext) {
        Environment env = applicationContext.getEnvironment();
        String keytabRef = env.getProperty("kafka.keytab-ref");
        String principal = env.getProperty("kafka.client-principal");
        try {
            String base64Keytab = getParameterFromSsm(keytabRef);
            Path keytabFile = writeKeytab(base64Keytab);

            Map<String, String> jaasVars = new HashMap<>();
            jaasVars.put("KEY_TAB_FILE", normalizePath(keytabFile.toAbsolutePath().toString()));
            jaasVars.put("KAFKA_CLIENT_PRINCIPAL", principal);
            Path jaasConf = renderTemplate("kerberos/jaas.conf", jaasVars, "jaas-");
            Path krb5Conf = renderTemplate("kerberos/krb5.conf", new HashMap<>(), "krb5-");

            System.setProperty("java.security.auth.login.config", jaasConf.toAbsolutePath().toString());
            System.setProperty("java.security.krb5.conf", krb5Conf.toAbsolutePath().toString());

            log.info("[Authentication] Keytab: {}", keytabFile);
            log.info("[Authentication] JAAS conf: {}", jaasConf);
            log.info("[Authentication] krb5 conf: {}", krb5Conf);
        } catch (Exception e) {
            log.error("[Authentication] Error authenticating", e);
            throw new RuntimeException("Failed to authenticate", e);
        }

    }

    private String getParameterFromSsm(String paramName) {
        try (SsmClient ssm = SsmClient.create()) {
            return ssm.getParameter(GetParameterRequest.builder()
                            .name(paramName)
                            .withDecryption(true)
                            .build())
                    .parameter()
                    .value();
        }
    }

    private Path writeKeytab(String base64Content) throws IOException {
        byte[] bytes = Base64.getDecoder().decode(base64Content);
        Path tempFile = Files.createTempFile("service-keytab-", ".keytab");
        Files.write(tempFile, bytes);
        return tempFile;
    }

    private Path renderTemplate(String resourcePath, Map<String, String> variables,
                                String prefix) throws IOException {
        ClassPathResource resource = new ClassPathResource(resourcePath);
        String content = Files.readString(resource.getFile().toPath());

        for (Map.Entry<String, String> entry : variables.entrySet()) {
            content = content.replace("${" + entry.getKey() + "}", entry.getValue());
        }

        Path tempFile = Files.createTempFile(prefix, ".conf");
        Files.writeString(tempFile, content);
        return tempFile;
    }

    private String normalizePath(String path) {
        return path.replace("\\", "/");
    }
}
