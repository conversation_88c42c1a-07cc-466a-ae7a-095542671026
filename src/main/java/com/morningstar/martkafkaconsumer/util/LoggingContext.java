package com.morningstar.martkafkaconsumer.util;

import org.slf4j.MDC;

public class LoggingContext {
    
    private static final String TRACE_ID_KEY = "traceId";

    public static void setTraceId(String traceId) {
        if (traceId != null && !traceId.trim().isEmpty()) {
            MDC.put(TRACE_ID_KEY, traceId);
        }
    }

    public static String getTraceId() {
        return MDC.get(TRACE_ID_KEY);
    }

    public static void clearTraceId() {
        MDC.remove(TRACE_ID_KEY);
    }

    public static void clearAll() {
        MDC.clear();
    }

    public static void withTraceId(String traceId, Runnable runnable) {
        String previousTraceId = getTraceId();
        try {
            setTraceId(traceId);
            runnable.run();
        } finally {
            if (previousTraceId != null) {
                setTraceId(previousTraceId);
            } else {
                clearTraceId();
            }
        }
    }
}
