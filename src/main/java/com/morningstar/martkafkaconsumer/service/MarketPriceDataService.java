package com.morningstar.martkafkaconsumer.service;

import com.morningstar.data.domain.ics.eod.enriched.CopyOverReasonEnum;
import com.morningstar.data.domain.proto.TsDataProtoBuf;
import com.morningstar.martkafkaconsumer.config.MarketPriceDataConfig;
import com.morningstar.martkafkaconsumer.exception.MessageProcessingException;
import com.morningstar.martkafkaconsumer.repository.RedisTsRepo;
import com.morningstar.martkafkaconsumer.util.Lz4Util;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Slf4j
public class MarketPriceDataService {

    private final RedisTsRepo redisTsRepo;
    private final MarketPriceDataConfig.MarketPriceData config;


    public MarketPriceDataService(RedisTsRepo redisTsRepo, MarketPriceDataConfig.MarketPriceData config) {
        this.redisTsRepo = redisTsRepo;
        this.config = config;
    }


    public void storeMarketPriceDetail(com.morningstar.data.domain.ics.eod.enriched.MarketPriceDetail marketPriceDetail) {

        storeMarketPriceDetailInternal(
            marketPriceDetail.getPerformanceId(),
            marketPriceDetail.getDate(),
            marketPriceDetail.getHigh(),
            marketPriceDetail.getLow(),
            marketPriceDetail.getOpen(),
            marketPriceDetail.getClose(),
            marketPriceDetail.getTradedVolume(),
            marketPriceDetail.getCopyOverReason()
        );

    }


    public void storeMarketPriceDetail(com.morningstar.data.domain.eod.events.MarketPriceDetail marketPriceDetail) {

        storeMarketPriceDetailInternal(
            marketPriceDetail.getPerformanceId(),
            marketPriceDetail.getDate(),
            marketPriceDetail.getHigh(),
            marketPriceDetail.getLow(),
            marketPriceDetail.getOpen(),
            marketPriceDetail.getClose(),
            marketPriceDetail.getTradedVolume(),
            null // Raw MarketPriceDetail doesn't have CopyOverReason
        );
    }

    private void storeMarketPriceDetailInternal(String investmentId, LocalDate date,
                                               BigDecimal high, BigDecimal low, BigDecimal open,
                                               BigDecimal close, BigDecimal tradedVolume,
                                               CopyOverReasonEnum copyOverReason) {
        if (investmentId == null || date == null) {
            log.warn("Missing required fields - investmentId: {}, date: {}, skipping storage",
                    investmentId, date);
            return;
        }

        try {
            Map<String, String> dataPointMapping = config.getDataPointMapping();

            Map<String, Object> dataPoints = Map.of(
                dataPointMapping.get("high"), getDoubleValue(high),
                dataPointMapping.get("low"), getDoubleValue(low),
                dataPointMapping.get("open"), getDoubleValue(open),
                dataPointMapping.get("close"), getDoubleValue(close),
                dataPointMapping.get("tradedVolume"), getDoubleValue(tradedVolume)
            );

            storeDataPointsBatch(investmentId, dataPoints, date, copyOverReason);
            log.debug("MarketPriceDataService: Stored market price data for investmentId: {}, date: {}, dataPoints: {}",
                    investmentId, date, dataPoints);

        } catch (Exception e) {
            log.error("Failed to store market price data for investmentId: {}, date: {}",
                    investmentId, date, e);
            throw new MessageProcessingException("Failed to store market price data", e);
        }
    }


    private void storeDataPointsBatch(String investmentId, Map<String, Object> dataPoints,
                                     LocalDate date, CopyOverReasonEnum copyOverReason) {
        try {
            int year = date.getYear();
            int keyYear = (year / config.getYearsPerKey()) * config.getYearsPerKey();
            int fieldStartYear = (year / config.getYearsPerField()) * config.getYearsPerField();

            String redisKey = config.getRedisKeyPrefix() + investmentId + ":" + keyYear;
            byte[] keyBytes = redisKey.getBytes(StandardCharsets.UTF_8);

            Map<String, byte[]> fieldBytesMap = new HashMap<>();
            for (String dpId : dataPoints.keySet()) {
                String field = dpId + ":" + fieldStartYear;
                byte[] fieldBytes = field.getBytes(StandardCharsets.UTF_8);
                fieldBytesMap.put(dpId, fieldBytes);
                log.debug("Field bytes for dpId: {}, field: {}, redisKey: {}", dpId, field, redisKey);
            }

            Map<String, TsDataProtoBuf.TSDataDouble> existingDataMap =
                getMultipleExistingData(keyBytes, fieldBytesMap, investmentId);

            Map<byte[], byte[]> batchData = new HashMap<>();
            for (Map.Entry<String, Object> entry : dataPoints.entrySet()) {
                String dpId = entry.getKey();
                double value = (Double) entry.getValue();
                byte[] fieldBytes = fieldBytesMap.get(dpId);

                TsDataProtoBuf.TSDataDouble existingData = existingDataMap.get(dpId);

                TsDataProtoBuf.TSDataDouble updatedData = mergeData(existingData, investmentId, dpId, value, date, copyOverReason);

                byte[] serializedData = updatedData.toByteArray();
                byte[] compressedData = Lz4Util.compress(serializedData);

                batchData.put(fieldBytes, compressedData);
            }

            setHashValueBatch(keyBytes, batchData);
            log.debug("Multiple values stored data points for key: {}", redisKey);

        } catch (Exception e) {
            log.error("Error in multiple values storing data points for investmentId: {}, date: {}, dataPoints: {}",
                    investmentId, date, dataPoints, e);
            throw new MessageProcessingException("Failed to multiple values store time series data", e);
        }
    }

    private Map<String, TsDataProtoBuf.TSDataDouble> getMultipleExistingData(byte[] keyBytes,
                                                                             Map<String, byte[]> fieldBytesMap,
                                                                             String investmentId) {

        List<byte[]> fieldBytesList = new ArrayList<>(fieldBytesMap.values());
        Map<byte[], byte[]> redisResults = redisTsRepo.getMultipleHashValues(keyBytes, fieldBytesList);

        Map<String, TsDataProtoBuf.TSDataDouble> results = new HashMap<>();

        for (Map.Entry<String, byte[]> entry : fieldBytesMap.entrySet()) {
            String dpId = entry.getKey();
            byte[] fieldBytes = entry.getValue();

            byte[] compressedData = redisResults.get(fieldBytes);
            TsDataProtoBuf.TSDataDouble existingData;

            if (compressedData != null && compressedData.length > 0) {
                try {
                    byte[] decompressed = Lz4Util.decompress(compressedData);
                    existingData = TsDataProtoBuf.TSDataDouble.parseFrom(decompressed);
                } catch (Exception e) {
                    log.error("Failed to decompress existing data for dpId: {}", dpId, e);
                    throw new RuntimeException("Failed to decompress existing data", e);
                }
            } else {
                existingData = createEmptyTSData(investmentId, dpId);
            }

            results.put(dpId, existingData);
        }

        return results;
    }

    private TsDataProtoBuf.TSDataDouble createEmptyTSData(String investmentId, String dpId) {
        return TsDataProtoBuf.TSDataDouble.newBuilder()
                .setInvestmentId(investmentId)
                .setDpId(dpId)
                .build();
    }

    private TsDataProtoBuf.TSDataDouble mergeData(TsDataProtoBuf.TSDataDouble existingData,
                                                  String investmentId, String dpId,
                                                  double value, LocalDate date, CopyOverReasonEnum copyOverReason) {
        long epochDay = date.toEpochDay();

        List<Long> dates = new ArrayList<>(existingData.getDatesList());
        List<Double> values = new ArrayList<>(existingData.getValuesList());
        List<Long> copyOverDates = new ArrayList<>(existingData.getCopyOverDatesList());
        List<Integer> copyOverReasons = new ArrayList<>(existingData.getCopyOverReasonsList());

        int insertIndex = findInsertPosition(dates, epochDay);

        if (insertIndex < dates.size() && dates.get(insertIndex).equals(epochDay)) {
            values.set(insertIndex, value);

            if (copyOverReason != null && copyOverReason != CopyOverReasonEnum.PRICE) {

                int copyOverIndex = copyOverDates.indexOf(epochDay);
                if (copyOverIndex >= 0) {
                    copyOverReasons.set(copyOverIndex, getCopyOverReasonValue(copyOverReason));
                } else {
                    copyOverDates.add(epochDay);
                    copyOverReasons.add(getCopyOverReasonValue(copyOverReason));
                }
            }
        } else {
            dates.add(insertIndex, epochDay);
            values.add(insertIndex, value);

            if (copyOverReason != null && copyOverReason != CopyOverReasonEnum.PRICE) {
                copyOverDates.add(epochDay);
                copyOverReasons.add(getCopyOverReasonValue(copyOverReason));
            }
        }

        TsDataProtoBuf.TSDataDouble.Builder builder = TsDataProtoBuf.TSDataDouble.newBuilder()
                .setInvestmentId(investmentId)
                .setDpId(dpId)
                .addAllDates(dates)
                .addAllValues(values)
                .addAllCopyOverDates(copyOverDates)
                .addAllCopyOverReasons(copyOverReasons);

        return builder.build();
    }

    private int findInsertPosition(List<Long> dates, long targetDate) {
        int left = 0;
        int right = dates.size();

        while (left < right) {
            int mid = left + (right - left) / 2;
            if (dates.get(mid) < targetDate) {
                left = mid + 1;
            } else {
                right = mid;
            }
        }

        return left;
    }

    private void setHashValueBatch(byte[] keyBytes, Map<byte[], byte[]> batchData) {
        redisTsRepo.setHashWithoutExpiration(keyBytes, batchData)
                .blockFirst();
    }

    private double getDoubleValue(BigDecimal bigDecimal) {
        if (bigDecimal == null) {
            log.error("BigDecimal is null, returning -1");
            return -1;
        }
        return bigDecimal.doubleValue();
    }

    private int getCopyOverReasonValue(CopyOverReasonEnum copyOverReason) {
        if (copyOverReason == null) {
            return -1;
        }

        return copyOverReason.ordinal();
    }
}
