package com.morningstar.martkafkaconsumer.consumer;

import com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent;
import com.morningstar.data.domain.ics.eod.enriched.MarketPriceDetail;
import com.morningstar.martkafkaconsumer.exception.MessageProcessingException;
import com.morningstar.martkafkaconsumer.service.MarketPriceDataService;
import com.morningstar.martkafkaconsumer.util.LoggingContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;

import javax.annotation.PreDestroy;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;


@Slf4j
public class MarketPriceEnrichedConsumer extends AbstractMessageConsumer<Long, MarketPriceEnrichedEvent> {

    private final MarketPriceDataService marketPriceDataService;
    private final ExecutorService executorService;

    public MarketPriceEnrichedConsumer(KafkaConsumerEndpoint<Long, MarketPriceEnrichedEvent> kafkaConsumerEndpoint,
                                       MarketPriceDataService marketPriceDataService,
                                       ExecutorService executorService) {
        super(kafkaConsumerEndpoint);
        this.marketPriceDataService = marketPriceDataService;
        this.executorService = executorService;
    }

    @Override
    protected void processMessages(Collection<MarketPriceEnrichedEvent> messages, String pollId) {

        log.info("MarketPriceEnrichedConsumer Processing {} messages with pollId: {}", messages.size(), pollId);

        long startTime = System.currentTimeMillis();

        List<CompletableFuture<Void>> futures = messages.stream()
                .map(message -> CompletableFuture.runAsync(() ->
                        LoggingContext.withTraceId(pollId, () -> processMessage(message)), executorService))
                .toList();

        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .join();

        long processingTime = System.currentTimeMillis() - startTime;
        log.info("MarketPriceEnrichedConsumer: Completed processing {} messages with pollId: {} in {}ms",
                messages.size(), pollId, processingTime);
    }

    private void processMessage(MarketPriceEnrichedEvent message) {
        if (message.getMarketPriceDataList() == null || message.getMarketPriceDataList().isEmpty()) {
            log.debug("MarketPriceEnrichedConsumer: No market price data found in message, skipping");
            return;
        }

        for (MarketPriceDetail marketPriceDetail : message.getMarketPriceDataList()) {
            try {
                marketPriceDataService.storeMarketPriceDetail(marketPriceDetail);
            } catch (Exception e) {
                log.error("MarketPriceEnrichedConsumer: Failed to process MarketPriceDetail for performanceId: {}, date: {}",
                        marketPriceDetail.getPerformanceId(), marketPriceDetail.getDate(), e);
                throw new MessageProcessingException("MarketPriceEnrichedConsumer: Failed to process message", e);
            }
        }
        log.info("MarketPriceEnrichedConsumer: Processed marketPriceDetail size: {}", message.getMarketPriceDataList().size());
    }

    @PreDestroy
    public void destroy() {
        executorService.shutdown();
    }
}
