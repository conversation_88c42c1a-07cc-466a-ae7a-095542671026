package com.morningstar.martkafkaconsumer.consumer;


import com.morningstar.data.domain.eod.events.MarketPriceDetail;
import com.morningstar.data.domain.eod.events.MarketPriceEvent;
import com.morningstar.martkafkaconsumer.exception.MessageProcessingException;
import com.morningstar.martkafkaconsumer.service.MarketPriceDataService;
import com.morningstar.martkafkaconsumer.util.LoggingContext;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.PreDestroy;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;

@Slf4j
public class MarketPriceRawConsumer extends AbstractMessageConsumer<Long, MarketPriceEvent> {
    private final MarketPriceDataService marketPriceDataService;
    private final ExecutorService executorService;

    public MarketPriceRawConsumer(KafkaConsumerEndpoint<Long, MarketPriceEvent> kafkaConsumerEndpoint,
                                  MarketPriceDataService marketPriceDataService,
                                  ExecutorService executorService) {
        super(kafkaConsumerEndpoint);
        this.marketPriceDataService = marketPriceDataService;
        this.executorService = executorService;
    }

    @Override
    protected void processMessages(Collection<MarketPriceEvent> messages, String pollId) {

        log.info("MarketPriceRawConsumer Processing {} messages with pollId: {}", messages.size(), pollId);

        long startTime = System.currentTimeMillis();

        List<CompletableFuture<Void>> futures = messages.stream()
                .map(message -> CompletableFuture.runAsync(() ->
                        LoggingContext.withTraceId(pollId, () -> processMessage(message)), executorService))
                .toList();

        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .join();

        long processingTime = System.currentTimeMillis() - startTime;
        log.info("MarketPriceRawConsumer: Completed processing {} messages with pollId: {} in {}ms",
                messages.size(), pollId, processingTime);

    }

    private void processMessage(MarketPriceEvent message) {
        if (message.getMarketPriceData() == null || message.getMarketPriceData().isEmpty()) {
            log.debug("MarketPriceRawConsumer: No market price data found in message, skipping");
            return;
        }

        for (MarketPriceDetail marketPriceDetail : message.getMarketPriceData()) {
            try {
                marketPriceDataService.storeMarketPriceDetail(marketPriceDetail);
            } catch (Exception e) {
                log.error("MarketPriceRawConsumer: Failed to process MarketPriceDetail for performanceId: {}, date: {}",
                        marketPriceDetail.getPerformanceId(), marketPriceDetail.getDate(), e);
                throw new MessageProcessingException("MarketPriceRawConsumer: Failed to process message", e);
            }
        }
        log.info("MarketPriceRawConsumer: Processed marketPriceDetail count: {}", message.getMarketPriceData().size());
    }

    @PreDestroy
    public void destroy() {
        executorService.shutdown();
    }
}
