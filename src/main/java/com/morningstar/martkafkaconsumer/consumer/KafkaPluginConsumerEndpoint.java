package com.morningstar.martkafkaconsumer.consumer;

import com.morningstar.dp.messaging.common.IConsumerListener;
import com.morningstar.dp.messaging.common.IKafkaFactory;
import com.morningstar.dp.messaging.common.impl.RunnableConsumer;
import com.morningstar.dp.messaging.common.util.ExtendedConfig;
import com.morningstar.dp.messaging.common.util.OffSetPartition;
import com.morningstar.martkafkaconsumer.util.LoggingContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.UUID;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

@Slf4j
public class KafkaPluginConsumerEndpoint<K, V> implements KafkaConsumerEndpoint<K, V> {

    private final String workflow;
    private final RunnableConsumer<K, V> runnableConsumer;
    private BiConsumer<Collection<V>, String> messageHandler;

    public KafkaPluginConsumerEndpoint(IKafkaFactory<K, V> kafkaPluginFactory, String workflow, String serdePojoClass, String clientId, String groupId) {
        this.workflow = workflow;

        Properties overrides = new Properties();
        overrides.setProperty(ConsumerConfig.CLIENT_ID_CONFIG, clientId);
        overrides.setProperty(ConsumerConfig.GROUP_ID_CONFIG, groupId);
        overrides.setProperty(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest");
        overrides.setProperty(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, "false");
        if ("devcloud".equals(System.getProperty("kafka.environment"))) {
            overrides.setProperty(ExtendedConfig.SERDE_POJO_CLASS_NAME_TAG, serdePojoClass);
        }

        KafkaPluginListener kafkaPluginListener = new KafkaPluginListener();

        this.runnableConsumer = kafkaPluginFactory.getConsumer(kafkaPluginListener, workflow, overrides);
        this.runnableConsumer.registerCommitCallback((map, e) -> {
            if (e == null) {
                map.forEach(
                        (key, value) -> log.info("Committed topic: {}, partition: {}, offset: {}", key.topic(), key.partition(), value.offset()));
            } else {
                log.error("Failed to commit offsets: {}", map, e);
            }
        });
    }

    @Override
    public void setMessageHandler(BiConsumer<Collection<V>, String> messageHandler) {

        this.messageHandler = messageHandler;

    }

    @Override
    public void start() {
        this.runnableConsumer.run();
    }

    @Override
    public String getWorkflow() {
        return this.workflow;
    }

    private Map<Integer, List<OffSetPartition>> groupOffsetsByPartitions(ConsumerRecords<K, V> consumerRecords) {
        Map<Integer, List<OffSetPartition>> groupOffsetsByPartitions = new HashMap<>();
        for (ConsumerRecord<K, V> record : consumerRecords) {
            int partition = record.partition();
            OffSetPartition offSetPartition = new OffSetPartition(record.offset(), partition, record.topic());
            groupOffsetsByPartitions.computeIfAbsent(partition, k -> new ArrayList<>()).add(offSetPartition);
        }
        return groupOffsetsByPartitions;
    }

    private List<OffSetPartition> getCommitOffsets(Map<Integer, List<OffSetPartition>> messageOffsets) {
        return messageOffsets.values().stream()
                .map(list -> {
                    OffSetPartition last = list.get(list.size() - 1);
                    return new OffSetPartition(last.getOffset() + 1, last.getPartition(), last.getTopic());
                })
                .collect(toList());
    }

    public class KafkaPluginListener implements IConsumerListener<ConsumerRecords<K, V>> {
        @Override
        public void notify(ConsumerRecords<K, V> records) {
            //log.info("receiving consumerRecords: {}", records);
            try {
                String pollId = UUID.randomUUID().toString();
                LoggingContext.setTraceId(pollId);
                List<V> values = new ArrayList<>();
                for (ConsumerRecord<K, V> record : records) {
                    values.add(record.value());
                    log.info("topic: {}, record partition {}, offset: {}, key: {}, pollId: {}, value: {}", record.topic(),
                            record.partition(), record.offset(), record.key(), pollId, record.value());
                }
                if (!values.isEmpty()) {
                    messageHandler.accept(values, pollId);
                }

                Map<Integer, List<OffSetPartition>> groupOffsetsByPartitions = groupOffsetsByPartitions(records);
                List<OffSetPartition> commitOffsets = getCommitOffsets(groupOffsetsByPartitions);
                log.info("Committing topic: {} offsets: {}", commitOffsets.get(0).getTopic(),
                        commitOffsets.stream().collect(Collectors.toMap(OffSetPartition::getPartition, OffSetPartition::getOffset)));
                try {
                    runnableConsumer.commitAsync(commitOffsets);
                } catch (RuntimeException ex) {
                    log.warn("Failed to commit offsets: {}", commitOffsets);
                }
            } finally {
                LoggingContext.clearTraceId();
            }
        }

        @Override
        public void onException(Exception ex) {
            log.error("Received IllegalStateException while consuming from kafka", ex);
        }
    }
}
