package com.morningstar.martkafkaconsumer.consumer;

import com.morningstar.martkafkaconsumer.exception.MessageProcessingException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;

import java.util.Collection;

@Slf4j
public abstract class AbstractMessageConsumer<K, V> implements CommandLineRunner {
    private final KafkaConsumerEndpoint<K, V> kafkaConsumerEndpoint;

    public AbstractMessageConsumer(KafkaConsumerEndpoint<K, V> kafkaConsumerEndpoint) {
        this.kafkaConsumerEndpoint = kafkaConsumerEndpoint;
    }

    @Override
    public void run(String... args) throws Exception {
        log.info("Starting Kafka message consumer for workflow: {}", kafkaConsumerEndpoint.getWorkflow());

        kafkaConsumerEndpoint.setMessageHandler(this::messageHandler);
        kafkaConsumerEndpoint.start();

        log.info("Kafka message consumer started successfully");
    }

    private void messageHandler(Collection<V> messages, String pollId) {

        log.info("Received {} messages with pollId: {}", messages.size(), pollId);
        try {
            processMessages(messages, pollId);
        } catch (Exception ex) {
            handleProcessingError(messages, pollId, ex);
            throw new MessageProcessingException("Failed to process messages", ex);
        }
    }

    protected abstract void processMessages(Collection<V> messages, String pollId);

    protected void handleProcessingError(Collection<V> messages, String pollId, Exception error) {
        log.error("Default error handling: failed to process {} messages, pollId: {}, messages: {}",
                messages.size(), pollId,  messages, error);
    }
}
