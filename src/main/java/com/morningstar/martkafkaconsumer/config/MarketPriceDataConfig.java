package com.morningstar.martkafkaconsumer.config;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;


@Data
@ConfigurationProperties(prefix = "market-price")
public class MarketPriceDataConfig {

    private MarketPriceData rawPriceData;
    private MarketPriceData adjustedPriceData;

    @Data
    public static class MarketPriceData {
        private String redisKeyPrefix;
        private int yearsPerKey = 10;
        private int yearsPerField = 2;
        private long defaultExpirationHours = 24;
        private Map<String, String> dataPointMapping;
    }

}
