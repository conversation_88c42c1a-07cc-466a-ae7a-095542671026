package com.morningstar.martkafkaconsumer.config;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.Map;

@Data
@ConfigurationProperties(prefix = "kafka")
public class KafkaProperties {
    private String region;
    private String environment;
    private String clientPrincipal;
    private String keytabRef;
    private String activeConsume;
    private Map<String, ConsumerEndpointConfig> consumerEndpoints;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ConsumerEndpointConfig {
        private String topic;
        private String clientId;
        private String groupId;
        private String keyDeserializer;
        private String valueDeserializer;
        private String serdePojoClass;
    }
}
