package com.morningstar.martkafkaconsumer.config;

import com.morningstar.dp.messaging.common.IKafkaFactory;
import com.morningstar.dp.messaging.common.impl.KafkaFacade;
import com.morningstar.martkafkaconsumer.consumer.KafkaConsumerEndpoint;
import com.morningstar.martkafkaconsumer.consumer.KafkaPluginConsumerEndpoint;
import org.apache.avro.specific.SpecificRecord;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@EnableConfigurationProperties(KafkaProperties.class)
public class KafkaConsumerConfig<K, V extends SpecificRecord> {

    @Bean
    @ConditionalOnMissingBean(IKafkaFactory.class)
    public IKafkaFactory<K, V> kafkaPluginFactory(KafkaProperties kafkaProperties) {
        KafkaFacade<K, V> kafkaFacade = new KafkaFacade<>(
                kafkaProperties.getRegion(),
                kafkaProperties.getEnvironment()
        );
        return kafkaFacade.getKafkaFactory();
    }

    @Bean
    @ConditionalOnMissingBean(name = "marketPriceListUpdatedEndpoint")
    public KafkaConsumerEndpoint<K, V> marketPriceListUpdatedEndpoint(
            IKafkaFactory<K, V> kafkaFactory,
            KafkaProperties kafkaProperties) {
        
        KafkaProperties.ConsumerEndpointConfig config = kafkaProperties.getConsumerEndpoints().get("market_price_list_updated");
        if (config == null) {
            throw new IllegalStateException("Consumer endpoint 'market_price_list_updated' not found in configuration");
        }
        
        return createKafkaConsumerEndpoint(kafkaFactory, config);
    }

    @Bean
    @ConditionalOnMissingBean(name = "marketPriceEnrichedEventEndpoint")
    public KafkaConsumerEndpoint<K, V> marketPriceEnrichedEventEndpoint(
            IKafkaFactory<K, V> kafkaFactory,
            KafkaProperties kafkaProperties) {
        
        KafkaProperties.ConsumerEndpointConfig config = kafkaProperties.getConsumerEndpoints().get("market_price_enriched_event");
        if (config == null) {
            throw new IllegalStateException("Consumer endpoint 'market_price_enriched_event' not found in configuration");
        }
        
        return createKafkaConsumerEndpoint(kafkaFactory, config);
    }

    private KafkaConsumerEndpoint<K, V> createKafkaConsumerEndpoint(
            IKafkaFactory<K, V> kafkaFactory,
            KafkaProperties.ConsumerEndpointConfig config) {

        return new KafkaPluginConsumerEndpoint<>(
                kafkaFactory,
                config.getTopic(),
                config.getSerdePojoClass(),
                config.getClientId(),
                config.getGroupId()
        );
    }
}
