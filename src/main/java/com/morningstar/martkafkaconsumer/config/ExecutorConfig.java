package com.morningstar.martkafkaconsumer.config;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Slf4j
@Configuration
public class ExecutorConfig {

    @Value("${executor.thread-count:#{T(java.lang.Runtime).getRuntime().availableProcessors()}}")
    private int threadCount;

    @Bean(name = "marketPriceExecutor")
    public ExecutorService marketPriceExecutor() {
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        log.info("Created MarketPrice ExecutorService with {} threads", threadCount);
        return executor;
    }

    @Bean(name = "marketPriceRawExecutor")
    public ExecutorService marketPriceRawExecutor() {
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        log.info("Created MarketPriceRaw ExecutorService with {} threads", threadCount);
        return executor;
    }

}
