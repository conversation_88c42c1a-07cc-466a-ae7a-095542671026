package com.morningstar.martkafkaconsumer.config;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Slf4j
@Configuration
public class ExecutorConfig {

    @Bean(name = "marketPriceExecutor")
    public ExecutorService marketPriceExecutor() {
        int threadCount = Runtime.getRuntime().availableProcessors();
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        log.info("Created MarketPrice ExecutorService with {} threads", threadCount);
        return executor;
    }


    @PreDestroy
    public void destroy() {
        marketPriceExecutor().shutdown();
    }

}
