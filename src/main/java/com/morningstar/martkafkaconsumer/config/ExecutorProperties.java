package com.morningstar.martkafkaconsumer.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

@Data
@ConfigurationProperties(prefix = "executor")
public class ExecutorProperties {
    
    private MarketPrice marketPrice = new MarketPrice();
    
    @Data
    public static class MarketPrice {
        private int threadCount = Runtime.getRuntime().availableProcessors();
    }
}
