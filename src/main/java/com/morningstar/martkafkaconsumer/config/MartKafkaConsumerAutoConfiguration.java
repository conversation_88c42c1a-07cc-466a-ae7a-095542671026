package com.morningstar.martkafkaconsumer.config;

import com.morningstar.data.domain.eod.events.MarketPriceDetail;
import com.morningstar.data.domain.eod.events.MarketPriceEvent;
import com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent;
import com.morningstar.dp.messaging.common.IKafkaFactory;
import com.morningstar.martkafkaconsumer.consumer.KafkaConsumerEndpoint;
import com.morningstar.martkafkaconsumer.consumer.KafkaPluginConsumerEndpoint;
import com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer;
import com.morningstar.martkafkaconsumer.consumer.MarketPriceRawConsumer;
import com.morningstar.martkafkaconsumer.repository.RedisTsRepo;
import com.morningstar.martkafkaconsumer.service.MarketPriceDataService;
import com.morningstar.martkafkaconsumer.util.RedisUtil;
import io.lettuce.core.cluster.ClusterClientOptions;
import io.lettuce.core.resource.ClientResources;
import io.lettuce.core.resource.DefaultClientResources;
import io.lettuce.core.resource.Delay;
import lombok.extern.slf4j.Slf4j;
import org.apache.avro.specific.SpecificRecord;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

@Slf4j
@Configuration
@EnableConfigurationProperties({RedisProperties.class, MarketPriceDataConfig.class})
public class MartKafkaConsumerAutoConfiguration<K, V extends SpecificRecord> {


    @Bean
    @ConditionalOnMissingBean(name = "marketPriceEnrichedConsumer")
    @ConditionalOnProperty(name = "kafka.active-consume", havingValue = "marketPriceEnrichedConsumer")
    public MarketPriceEnrichedConsumer marketPriceEnrichedConsumer(
            @Qualifier("marketPriceEnrichedEventEndpoint") KafkaConsumerEndpoint<Long, MarketPriceEnrichedEvent> endpoint,
            @Qualifier("adjustedPriceDataService") MarketPriceDataService marketPriceDataService,
            @Qualifier("marketPriceExecutor") ExecutorService executorService) {
        return new MarketPriceEnrichedConsumer(endpoint, marketPriceDataService, executorService);
    }


    @Bean
    @ConditionalOnMissingBean(name = "marketPriceRawConsumer")
    @ConditionalOnProperty(name = "kafka.active-consume", havingValue = "marketPriceRawConsumer")
    public MarketPriceRawConsumer marketPriceRawConsumer(
            @Qualifier("marketPriceListUpdatedEndpoint") KafkaConsumerEndpoint<Long, MarketPriceEvent> endpoint,
            @Qualifier("rawPriceDataService") MarketPriceDataService marketPriceDataService,
            @Qualifier("marketPriceExecutor") ExecutorService executorService) {
        return new MarketPriceRawConsumer(endpoint, marketPriceDataService, executorService);
    }

    @Bean("rawPriceDataService")
    @ConditionalOnMissingBean(name = "rawPriceDataService")
    @ConditionalOnProperty(name = "kafka.active-consume", havingValue = "marketPriceRawConsumer")
    public MarketPriceDataService rawPriceDataService(@Qualifier("tsRepo") RedisTsRepo redisTsRepo,
                                                      MarketPriceDataConfig config) {
        return new MarketPriceDataService(redisTsRepo, config.getRawPriceData());
    }

    @Bean("adjustedPriceDataService")
    @ConditionalOnMissingBean(name = "adjustedPriceDataService")
    @ConditionalOnProperty(name = "kafka.active-consume", havingValue = "marketPriceEnrichedConsumer")
    public MarketPriceDataService adjustedPriceDataService(@Qualifier("tsRepo") RedisTsRepo redisTsRepo,
                                                           MarketPriceDataConfig config) {
        return new MarketPriceDataService(redisTsRepo, config.getAdjustedPriceData());
    }

    @Bean(name = "tsRepo")
    @ConditionalOnMissingBean(name = "tsRepo")
    public RedisTsRepo tsRepo(ClientResources clientResources, ClusterClientOptions clientOptions, RedisProperties redisProperties) {
        return new RedisTsRepo(RedisUtil.createLettuceConnectionFactories(
                redisProperties.getTsData().getHost(),
                redisProperties.getTsData().getPort(),
                redisProperties.getClientName(),
                10,
                clientResources,
                clientOptions));
    }

    @Bean(name = "clientOptions")
    public ClusterClientOptions clientOptions() {
        return ClusterClientOptions.builder()
                .suspendReconnectOnProtocolFailure(true)
                .autoReconnect(true)
                .build();
    }

    @Bean(name = "clientResources")
    public static ClientResources getClientResources() {
        int cpuCount = Runtime.getRuntime().availableProcessors();
        return DefaultClientResources.builder()
                .ioThreadPoolSize(cpuCount * 2)
                .computationThreadPoolSize(cpuCount)
                .reconnectDelay(Delay.exponential(Duration.ofMillis(100), Duration.ofSeconds(30), 2, TimeUnit.MILLISECONDS))
                .build();
    }

}
