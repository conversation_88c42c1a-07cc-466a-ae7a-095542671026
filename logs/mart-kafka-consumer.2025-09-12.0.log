2025-09-12 17:15:28.610 [main] ERROR [] c.m.m.security.AuthInitializer -- [Authentication] Error authenticating
software.amazon.awssdk.core.exception.SdkClientException: Unable to load credentials from any of the providers in the chain AwsCredentialsProviderChain(credentialsProviders=[SystemPropertyCredentialsProvider(), EnvironmentVariableCredentialsProvider(), WebIdentityTokenCredentialsProvider(), ProfileCredentialsProvider(profileName=default, profileFile=ProfileFile(profilesAndSectionsMap=[{default=Profile(name=default, properties=[sso_region, sso_start_url, sso_role_name, region, sso_account_id]), 619071339464_mstar-operator=Profile(name=619071339464_mstar-operator, properties=[aws_access_key_id, aws_session_token, aws_secret_access_key]), 270863951168_mstar-operator=Profile(name=270863951168_mstar-operator, properties=[aws_access_key_id, aws_session_token, aws_secret_access_key]), 921072466220_mstar-readonly=Profile(name=921072466220_mstar-readonly, properties=[aws_access_key_id, aws_session_token, aws_secret_access_key])}, {}])), ContainerCredentialsProvider(), InstanceProfileCredentialsProvider()]) : [SystemPropertyCredentialsProvider(): Unable to load credentials from system settings. Access key must be specified either via environment variable (AWS_ACCESS_KEY_ID) or system property (aws.accessKeyId)., EnvironmentVariableCredentialsProvider(): Unable to load credentials from system settings. Access key must be specified either via environment variable (AWS_ACCESS_KEY_ID) or system property (aws.accessKeyId)., WebIdentityTokenCredentialsProvider(): Either the environment variable AWS_WEB_IDENTITY_TOKEN_FILE or the javaproperty aws.webIdentityTokenFile must be set., ProfileCredentialsProvider(profileName=default, profileFile=ProfileFile(profilesAndSectionsMap=[{default=Profile(name=default, properties=[sso_region, sso_start_url, sso_role_name, region, sso_account_id]), 619071339464_mstar-operator=Profile(name=619071339464_mstar-operator, properties=[aws_access_key_id, aws_session_token, aws_secret_access_key]), 270863951168_mstar-operator=Profile(name=270863951168_mstar-operator, properties=[aws_access_key_id, aws_session_token, aws_secret_access_key]), 921072466220_mstar-readonly=Profile(name=921072466220_mstar-readonly, properties=[aws_access_key_id, aws_session_token, aws_secret_access_key])}, {}])): To use Sso related properties in the 'default' profile, the 'sso' service module must be on the class path., ContainerCredentialsProvider(): Cannot fetch credentials from container - neither AWS_CONTAINER_CREDENTIALS_FULL_URI or AWS_CONTAINER_CREDENTIALS_RELATIVE_URI environment variables are set., InstanceProfileCredentialsProvider(): Failed to load credentials from IMDS.]
	at software.amazon.awssdk.core.exception.SdkClientException$BuilderImpl.build(SdkClientException.java:111)
	at software.amazon.awssdk.auth.credentials.AwsCredentialsProviderChain.resolveCredentials(AwsCredentialsProviderChain.java:117)
	at software.amazon.awssdk.auth.credentials.internal.LazyAwsCredentialsProvider.resolveCredentials(LazyAwsCredentialsProvider.java:45)
	at software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider.resolveCredentials(DefaultCredentialsProvider.java:126)
	at software.amazon.awssdk.core.internal.util.MetricUtils.measureDuration(MetricUtils.java:50)
	at software.amazon.awssdk.awscore.internal.authcontext.AwsCredentialsAuthorizationStrategy.resolveCredentials(AwsCredentialsAuthorizationStrategy.java:100)
	at software.amazon.awssdk.awscore.internal.authcontext.AwsCredentialsAuthorizationStrategy.addCredentialsToExecutionAttributes(AwsCredentialsAuthorizationStrategy.java:77)
	at software.amazon.awssdk.awscore.internal.AwsExecutionContextBuilder.invokeInterceptorsAndCreateExecutionContext(AwsExecutionContextBuilder.java:123)
	at software.amazon.awssdk.awscore.client.handler.AwsSyncClientHandler.invokeInterceptorsAndCreateExecutionContext(AwsSyncClientHandler.java:69)
	at software.amazon.awssdk.core.internal.handler.BaseSyncClientHandler.lambda$execute$1(BaseSyncClientHandler.java:78)
	at software.amazon.awssdk.core.internal.handler.BaseSyncClientHandler.measureApiCallSuccess(BaseSyncClientHandler.java:179)
	at software.amazon.awssdk.core.internal.handler.BaseSyncClientHandler.execute(BaseSyncClientHandler.java:76)
	at software.amazon.awssdk.core.client.handler.SdkSyncClientHandler.execute(SdkSyncClientHandler.java:45)
	at software.amazon.awssdk.awscore.client.handler.AwsSyncClientHandler.execute(AwsSyncClientHandler.java:56)
	at software.amazon.awssdk.services.ssm.DefaultSsmClient.getParameter(DefaultSsmClient.java:8698)
	at com.morningstar.martkafkaconsumer.security.AuthInitializer.getParameterFromSsm(AuthInitializer.java:50)
	at com.morningstar.martkafkaconsumer.security.AuthInitializer.initialize(AuthInitializer.java:26)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:612)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:383)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:317)
	at com.morningstar.martkafkaconsumer.MartKafkaConsumerApplication.main(MartKafkaConsumerApplication.java:13)
2025-09-12 17:15:28.634 [main] ERROR [] o.s.boot.SpringApplication -- Application run failed
java.lang.RuntimeException: Failed to authenticate
	at com.morningstar.martkafkaconsumer.security.AuthInitializer.initialize(AuthInitializer.java:43)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:612)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:383)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:317)
	at com.morningstar.martkafkaconsumer.MartKafkaConsumerApplication.main(MartKafkaConsumerApplication.java:13)
Caused by: software.amazon.awssdk.core.exception.SdkClientException: Unable to load credentials from any of the providers in the chain AwsCredentialsProviderChain(credentialsProviders=[SystemPropertyCredentialsProvider(), EnvironmentVariableCredentialsProvider(), WebIdentityTokenCredentialsProvider(), ProfileCredentialsProvider(profileName=default, profileFile=ProfileFile(profilesAndSectionsMap=[{default=Profile(name=default, properties=[sso_region, sso_start_url, sso_role_name, region, sso_account_id]), 619071339464_mstar-operator=Profile(name=619071339464_mstar-operator, properties=[aws_access_key_id, aws_session_token, aws_secret_access_key]), 270863951168_mstar-operator=Profile(name=270863951168_mstar-operator, properties=[aws_access_key_id, aws_session_token, aws_secret_access_key]), 921072466220_mstar-readonly=Profile(name=921072466220_mstar-readonly, properties=[aws_access_key_id, aws_session_token, aws_secret_access_key])}, {}])), ContainerCredentialsProvider(), InstanceProfileCredentialsProvider()]) : [SystemPropertyCredentialsProvider(): Unable to load credentials from system settings. Access key must be specified either via environment variable (AWS_ACCESS_KEY_ID) or system property (aws.accessKeyId)., EnvironmentVariableCredentialsProvider(): Unable to load credentials from system settings. Access key must be specified either via environment variable (AWS_ACCESS_KEY_ID) or system property (aws.accessKeyId)., WebIdentityTokenCredentialsProvider(): Either the environment variable AWS_WEB_IDENTITY_TOKEN_FILE or the javaproperty aws.webIdentityTokenFile must be set., ProfileCredentialsProvider(profileName=default, profileFile=ProfileFile(profilesAndSectionsMap=[{default=Profile(name=default, properties=[sso_region, sso_start_url, sso_role_name, region, sso_account_id]), 619071339464_mstar-operator=Profile(name=619071339464_mstar-operator, properties=[aws_access_key_id, aws_session_token, aws_secret_access_key]), 270863951168_mstar-operator=Profile(name=270863951168_mstar-operator, properties=[aws_access_key_id, aws_session_token, aws_secret_access_key]), 921072466220_mstar-readonly=Profile(name=921072466220_mstar-readonly, properties=[aws_access_key_id, aws_session_token, aws_secret_access_key])}, {}])): To use Sso related properties in the 'default' profile, the 'sso' service module must be on the class path., ContainerCredentialsProvider(): Cannot fetch credentials from container - neither AWS_CONTAINER_CREDENTIALS_FULL_URI or AWS_CONTAINER_CREDENTIALS_RELATIVE_URI environment variables are set., InstanceProfileCredentialsProvider(): Failed to load credentials from IMDS.]
	at software.amazon.awssdk.core.exception.SdkClientException$BuilderImpl.build(SdkClientException.java:111)
	at software.amazon.awssdk.auth.credentials.AwsCredentialsProviderChain.resolveCredentials(AwsCredentialsProviderChain.java:117)
	at software.amazon.awssdk.auth.credentials.internal.LazyAwsCredentialsProvider.resolveCredentials(LazyAwsCredentialsProvider.java:45)
	at software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider.resolveCredentials(DefaultCredentialsProvider.java:126)
	at software.amazon.awssdk.core.internal.util.MetricUtils.measureDuration(MetricUtils.java:50)
	at software.amazon.awssdk.awscore.internal.authcontext.AwsCredentialsAuthorizationStrategy.resolveCredentials(AwsCredentialsAuthorizationStrategy.java:100)
	at software.amazon.awssdk.awscore.internal.authcontext.AwsCredentialsAuthorizationStrategy.addCredentialsToExecutionAttributes(AwsCredentialsAuthorizationStrategy.java:77)
	at software.amazon.awssdk.awscore.internal.AwsExecutionContextBuilder.invokeInterceptorsAndCreateExecutionContext(AwsExecutionContextBuilder.java:123)
	at software.amazon.awssdk.awscore.client.handler.AwsSyncClientHandler.invokeInterceptorsAndCreateExecutionContext(AwsSyncClientHandler.java:69)
	at software.amazon.awssdk.core.internal.handler.BaseSyncClientHandler.lambda$execute$1(BaseSyncClientHandler.java:78)
	at software.amazon.awssdk.core.internal.handler.BaseSyncClientHandler.measureApiCallSuccess(BaseSyncClientHandler.java:179)
	at software.amazon.awssdk.core.internal.handler.BaseSyncClientHandler.execute(BaseSyncClientHandler.java:76)
	at software.amazon.awssdk.core.client.handler.SdkSyncClientHandler.execute(SdkSyncClientHandler.java:45)
	at software.amazon.awssdk.awscore.client.handler.AwsSyncClientHandler.execute(AwsSyncClientHandler.java:56)
	at software.amazon.awssdk.services.ssm.DefaultSsmClient.getParameter(DefaultSsmClient.java:8698)
	at com.morningstar.martkafkaconsumer.security.AuthInitializer.getParameterFromSsm(AuthInitializer.java:50)
	at com.morningstar.martkafkaconsumer.security.AuthInitializer.initialize(AuthInitializer.java:26)
	... 4 common frames omitted
2025-09-12 17:17:02.686 [main] INFO  [] c.m.m.security.AuthInitializer -- [Authentication] Keytab: C:\Users\<USER>\AppData\Local\Temp\service-keytab-13629341620912170606.keytab
2025-09-12 17:17:02.687 [main] INFO  [] c.m.m.security.AuthInitializer -- [Authentication] JAAS conf: C:\Users\<USER>\AppData\Local\Temp\jaas-4638497705578589715.conf
2025-09-12 17:17:02.687 [main] INFO  [] c.m.m.security.AuthInitializer -- [Authentication] krb5 conf: C:\Users\<USER>\AppData\Local\Temp\krb5-4613117916542487509.conf
2025-09-12 17:17:02.690 [main] INFO  [] c.m.m.MartKafkaConsumerApplication -- Starting MartKafkaConsumerApplication using Java 17.0.9 with PID 8368 (C:\Zeng\dataac\msstash\mart-kafka-consumer\target\classes started by wzeng1 in C:\Zeng\dataac\msstash\mart-kafka-consumer)
2025-09-12 17:17:02.691 [main] DEBUG [] c.m.m.MartKafkaConsumerApplication -- Running with Spring Boot v3.5.5, Spring v6.2.10
2025-09-12 17:17:02.691 [main] INFO  [] c.m.m.MartKafkaConsumerApplication -- The following 1 profile is active: "dev"
2025-09-12 17:17:03.021 [main] INFO  [] o.s.d.r.c.RepositoryConfigurationDelegate -- Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-12 17:17:03.024 [main] INFO  [] o.s.d.r.c.RepositoryConfigurationDelegate -- Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-12 17:17:03.057 [main] INFO  [] o.s.d.r.c.RepositoryConfigurationDelegate -- Finished Spring Data repository scanning in 14 ms. Found 0 Redis repository interfaces.
2025-09-12 17:17:03.233 [main] INFO  [] c.m.m.config.ExecutorConfig -- Created MarketPrice ExecutorService with 20 threads
2025-09-12 17:17:04.965 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Kafka factory fully initialized with region (aws-us-east-1) and env (dev)
2025-09-12 17:17:04.983 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- [DEPRECATED-Should not be here] - Done setting a system property for serde class (key:serde_pojo_class_name_tag, value:com.morningstar.data.domain.eod.events.MarketPriceEvent)
2025-09-12 17:17:04.986 [main] INFO  [] c.m.d.m.c.s.a.AvroNoSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.eod.events.MarketPriceEvent, codecName: null)
2025-09-12 17:17:06.012 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 17:17:06.014 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- This generic serde is using (serdePojoClassName: class com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 17:17:06.109 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- About to send an 'adminMessage' to this workflow (clients_connections_details) about this client connection: ProducerRecord(topic=dev_clients_connections_details, partition=null, headers=RecordHeaders(headers = [], isReadOnly = false), key=market_price_list_updated|DPDA_Market_Price_Consumer|CA-JT26LR3, value={"clientId": "DPDA_Market_Price_Consumer", "workflowId": "market_price_list_updated", "connectionDescriptor": {"region": "aws-us-east-1", "env": "dev", "hostname": "CA-JT26LR3", "port": -1, "pid": 8368, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757711826011", "endDateTime": null, "error": null}, timestamp=null)
2025-09-12 17:17:16.698 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Sent an 'adminMessage' to this workflow (clients_connections_details) about this client connection: ProducerRecord(topic=dev_clients_connections_details, partition=null, headers=RecordHeaders(headers = [], isReadOnly = true), key=market_price_list_updated|DPDA_Market_Price_Consumer|CA-JT26LR3, value={"clientId": "DPDA_Market_Price_Consumer", "workflowId": "market_price_list_updated", "connectionDescriptor": {"region": "aws-us-east-1", "env": "dev", "hostname": "CA-JT26LR3", "port": -1, "pid": 8368, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757711826011", "endDateTime": null, "error": null}, timestamp=null)
2025-09-12 17:17:17.488 [kafka-producer-network-thread | producer-1] ERROR [] o.a.k.c.producer.internals.Sender -- [Producer clientId=producer-1] Aborting producer batches due to fatal error
org.apache.kafka.common.errors.ClusterAuthorizationException: Cluster authorization failed.
2025-09-12 17:17:17.504 [main] WARN  [] c.m.d.m.common.impl.KafkaFactory -- This method is being deprecated, we should not use it going forward! Please use other overloaded methods. Done building fully configured consumer (org.apache.kafka.clients.consumer.KafkaConsumer@3ec06e6d). 
Morningstar custom properties:
	{tasks_number_tag=1, serde_pojo_class_name_tag=com.morningstar.data.domain.eod.events.MarketPriceEvent, topic_name_tag=dev_market_price_list_updated}. 
This Consumer Connection Details:
	{"clientId": "DPDA_Market_Price_Consumer", "workflowId": "market_price_list_updated", "connectionDescriptor": {"region": "aws-us-east-1", "env": "dev", "hostname": "CA-JT26LR3", "port": -1, "pid": 8368, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757711826011", "endDateTime": null, "error": null}
2025-09-12 17:17:17.520 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- workflow market_price_list_updated enable.auto.commit is configured as false
2025-09-12 17:17:17.521 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- Done initialize this 'RunnableConsumer' - Details:
	Workflow: market_price_list_updated
	Overrides: [{enable.auto.commit=false, group.id=dpda_market_price_group, auto.offset.reset=earliest, client.id=DPDA_Market_Price_Consumer}]
2025-09-12 17:17:17.522 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Done building fully configured consumer (com.morningstar.dp.messaging.common.impl.RunnableConsumer@2a25963b)
2025-09-12 17:17:17.524 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- [DEPRECATED-Should not be here] - Done setting a system property for serde class (key:serde_pojo_class_name_tag, value:com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent)
2025-09-12 17:17:17.525 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent, codecName: null)
2025-09-12 17:17:17.527 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- This generic serde is using (serdePojoClassName: class com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent, codecName: null)
2025-09-12 17:17:17.527 [main] WARN  [] c.m.d.m.common.impl.KafkaFactory -- client id DPDA_Market_Price_Consumer already exists, will change it to DPDA_Market_Price_Consumer-0
2025-09-12 17:17:17.533 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 17:17:17.533 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- This generic serde is using (serdePojoClassName: class com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 17:17:17.533 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- About to send an 'adminMessage' to this workflow (clients_connections_details) about this client connection: ProducerRecord(topic=dev_clients_connections_details, partition=null, headers=RecordHeaders(headers = [], isReadOnly = false), key=market_price_enriched_event|DPDA_Market_Price_Consumer-0|CA-JT26LR3, value={"clientId": "DPDA_Market_Price_Consumer-0", "workflowId": "market_price_enriched_event", "connectionDescriptor": {"region": "aws-us-east-1", "env": "dev", "hostname": "CA-JT26LR3", "port": -1, "pid": 8368, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757711837533", "endDateTime": null, "error": null}, timestamp=null)
2025-09-12 17:17:17.533 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- This is for auditing only so no issue to worry about. Will not produce into the kafka-Plugin admin topic (clients_connections_details) for now.
2025-09-12 17:17:17.533 [main] WARN  [] c.m.d.m.common.impl.KafkaFactory -- This method is being deprecated, we should not use it going forward! Please use other overloaded methods. Done building fully configured consumer (org.apache.kafka.clients.consumer.KafkaConsumer@70a8159f). 
Morningstar custom properties:
	{tasks_number_tag=1, serde_pojo_class_name_tag=com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent, topic_name_tag=dev_market_price_enriched_event}. 
This Consumer Connection Details:
	{"clientId": "DPDA_Market_Price_Consumer-0", "workflowId": "market_price_enriched_event", "connectionDescriptor": {"region": "aws-us-east-1", "env": "dev", "hostname": "CA-JT26LR3", "port": -1, "pid": 8368, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757711837533", "endDateTime": null, "error": null}
2025-09-12 17:17:17.534 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- workflow market_price_enriched_event enable.auto.commit is configured as false
2025-09-12 17:17:17.534 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- Done initialize this 'RunnableConsumer' - Details:
	Workflow: market_price_enriched_event
	Overrides: [{enable.auto.commit=false, group.id=dpda_market_price_group, auto.offset.reset=earliest, client.id=DPDA_Market_Price_Consumer}]
2025-09-12 17:17:17.534 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Done building fully configured consumer (com.morningstar.dp.messaging.common.impl.RunnableConsumer@2c0e38c2)
2025-09-12 17:17:18.061 [main] INFO  [] c.m.m.MartKafkaConsumerApplication -- Started MartKafkaConsumerApplication in 19.834 seconds (process running for 20.646)
2025-09-12 17:17:18.064 [main] INFO  [] c.m.m.c.AbstractMessageConsumer -- Starting Kafka message consumer for topic: market_price_enriched_event
2025-09-12 17:17:18.065 [main] INFO  [] c.m.m.c.AbstractMessageConsumer -- Kafka message consumer started successfully
2025-09-12 17:19:23.524 [runnable-consumer-0] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- Trying to close Kafka consumer, RunnableConsumer.isRunning: false
2025-09-12 17:36:43.207 [main] INFO  [] c.m.m.security.AuthInitializer -- [Authentication] Keytab: C:\Users\<USER>\AppData\Local\Temp\service-keytab-3059143355026634737.keytab
2025-09-12 17:36:43.211 [main] INFO  [] c.m.m.security.AuthInitializer -- [Authentication] JAAS conf: C:\Users\<USER>\AppData\Local\Temp\jaas-13101684147949066267.conf
2025-09-12 17:36:43.211 [main] INFO  [] c.m.m.security.AuthInitializer -- [Authentication] krb5 conf: C:\Users\<USER>\AppData\Local\Temp\krb5-18440100709146139467.conf
2025-09-12 17:36:43.217 [main] INFO  [] c.m.m.MartKafkaConsumerApplication -- Starting MartKafkaConsumerApplication using Java 17.0.9 with PID 31712 (C:\Zeng\dataac\msstash\mart-kafka-consumer\target\classes started by wzeng1 in C:\Zeng\dataac\msstash\mart-kafka-consumer)
2025-09-12 17:36:43.218 [main] DEBUG [] c.m.m.MartKafkaConsumerApplication -- Running with Spring Boot v3.5.5, Spring v6.2.10
2025-09-12 17:36:43.220 [main] INFO  [] c.m.m.MartKafkaConsumerApplication -- The following 1 profile is active: "dev"
2025-09-12 17:36:43.552 [main] INFO  [] o.s.d.r.c.RepositoryConfigurationDelegate -- Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-12 17:36:43.553 [main] INFO  [] o.s.d.r.c.RepositoryConfigurationDelegate -- Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-12 17:36:43.581 [main] INFO  [] o.s.d.r.c.RepositoryConfigurationDelegate -- Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces.
2025-09-12 17:36:43.735 [main] INFO  [] c.m.m.config.ExecutorConfig -- Created MarketPrice ExecutorService with 20 threads
2025-09-12 17:36:45.192 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Kafka factory fully initialized with region (aws-us-east-1) and env (dev)
2025-09-12 17:36:45.208 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- [DEPRECATED-Should not be here] - Done setting a system property for serde class (key:serde_pojo_class_name_tag, value:com.morningstar.data.domain.eod.events.MarketPriceEvent)
2025-09-12 17:36:45.211 [main] INFO  [] c.m.d.m.c.s.a.AvroNoSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.eod.events.MarketPriceEvent, codecName: null)
2025-09-12 17:36:45.765 [main] WARN  [] o.a.k.c.consumer.ConsumerConfig -- The configuration 'schema.registry.url' was supplied but isn't a known config.
2025-09-12 17:36:45.776 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 17:36:45.777 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- This generic serde is using (serdePojoClassName: class com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 17:36:45.853 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- About to send an 'adminMessage' to this workflow (clients_connections_details) about this client connection: ProducerRecord(topic=dev_clients_connections_details, partition=null, headers=RecordHeaders(headers = [], isReadOnly = false), key=market_price_list_updated|DPDA_Market_Price_Consumer|CA-JT26LR3, value={"clientId": "DPDA_Market_Price_Consumer", "workflowId": "market_price_list_updated", "connectionDescriptor": {"region": "aws-us-east-1", "env": "dev", "hostname": "CA-JT26LR3", "port": -1, "pid": 31712, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-2.0.0.jar", "version": "kafka-clients-2.0.0.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757713005774", "endDateTime": null, "error": null}, timestamp=null)
2025-09-12 17:37:00.840 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Sent an 'adminMessage' to this workflow (clients_connections_details) about this client connection: ProducerRecord(topic=dev_clients_connections_details, partition=null, headers=RecordHeaders(headers = [], isReadOnly = true), key=market_price_list_updated|DPDA_Market_Price_Consumer|CA-JT26LR3, value={"clientId": "DPDA_Market_Price_Consumer", "workflowId": "market_price_list_updated", "connectionDescriptor": {"region": "aws-us-east-1", "env": "dev", "hostname": "CA-JT26LR3", "port": -1, "pid": 31712, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-2.0.0.jar", "version": "kafka-clients-2.0.0.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757713005774", "endDateTime": null, "error": null}, timestamp=null)
2025-09-12 17:37:01.552 [main] WARN  [] c.m.d.m.common.impl.KafkaFactory -- This method is being deprecated, we should not use it going forward! Please use other overloaded methods. Done building fully configured consumer (org.apache.kafka.clients.consumer.KafkaConsumer@1864eba6). 
Morningstar custom properties:
	{tasks_number_tag=1, serde_pojo_class_name_tag=com.morningstar.data.domain.eod.events.MarketPriceEvent, topic_name_tag=dev_market_price_list_updated}. 
This Consumer Connection Details:
	{"clientId": "DPDA_Market_Price_Consumer", "workflowId": "market_price_list_updated", "connectionDescriptor": {"region": "aws-us-east-1", "env": "dev", "hostname": "CA-JT26LR3", "port": -1, "pid": 31712, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-2.0.0.jar", "version": "kafka-clients-2.0.0.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757713005774", "endDateTime": null, "error": null}
2025-09-12 17:37:01.565 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- workflow market_price_list_updated enable.auto.commit is configured as false
2025-09-12 17:37:01.567 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- Done initialize this 'RunnableConsumer' - Details:
	Workflow: market_price_list_updated
	Overrides: [{enable.auto.commit=false, group.id=dpda_market_price_group, auto.offset.reset=earliest, client.id=DPDA_Market_Price_Consumer}]
2025-09-12 17:37:01.567 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Done building fully configured consumer (com.morningstar.dp.messaging.common.impl.RunnableConsumer@43a2820d)
2025-09-12 17:37:01.568 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- [DEPRECATED-Should not be here] - Done setting a system property for serde class (key:serde_pojo_class_name_tag, value:com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent)
2025-09-12 17:37:01.568 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent, codecName: null)
2025-09-12 17:37:01.572 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- This generic serde is using (serdePojoClassName: class com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent, codecName: null)
2025-09-12 17:37:01.572 [main] WARN  [] c.m.d.m.common.impl.KafkaFactory -- client id DPDA_Market_Price_Consumer already exists, will change it to DPDA_Market_Price_Consumer-0
2025-09-12 17:37:01.577 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 17:37:01.577 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- This generic serde is using (serdePojoClassName: class com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 17:37:01.577 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- About to send an 'adminMessage' to this workflow (clients_connections_details) about this client connection: ProducerRecord(topic=dev_clients_connections_details, partition=null, headers=RecordHeaders(headers = [], isReadOnly = false), key=market_price_enriched_event|DPDA_Market_Price_Consumer-0|CA-JT26LR3, value={"clientId": "DPDA_Market_Price_Consumer-0", "workflowId": "market_price_enriched_event", "connectionDescriptor": {"region": "aws-us-east-1", "env": "dev", "hostname": "CA-JT26LR3", "port": -1, "pid": 31712, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-2.0.0.jar", "version": "kafka-clients-2.0.0.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757713021577", "endDateTime": null, "error": null}, timestamp=null)
2025-09-12 17:37:01.578 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- This is for auditing only so no issue to worry about. Will not produce into the kafka-Plugin admin topic (clients_connections_details) for now.
2025-09-12 17:37:01.578 [main] WARN  [] c.m.d.m.common.impl.KafkaFactory -- This method is being deprecated, we should not use it going forward! Please use other overloaded methods. Done building fully configured consumer (org.apache.kafka.clients.consumer.KafkaConsumer@2fb8d338). 
Morningstar custom properties:
	{tasks_number_tag=1, serde_pojo_class_name_tag=com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent, topic_name_tag=dev_market_price_enriched_event}. 
This Consumer Connection Details:
	{"clientId": "DPDA_Market_Price_Consumer-0", "workflowId": "market_price_enriched_event", "connectionDescriptor": {"region": "aws-us-east-1", "env": "dev", "hostname": "CA-JT26LR3", "port": -1, "pid": 31712, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-2.0.0.jar", "version": "kafka-clients-2.0.0.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757713021577", "endDateTime": null, "error": null}
2025-09-12 17:37:01.578 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- workflow market_price_enriched_event enable.auto.commit is configured as false
2025-09-12 17:37:01.578 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- Done initialize this 'RunnableConsumer' - Details:
	Workflow: market_price_enriched_event
	Overrides: [{enable.auto.commit=false, group.id=dpda_market_price_group, auto.offset.reset=earliest, client.id=DPDA_Market_Price_Consumer}]
2025-09-12 17:37:01.579 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Done building fully configured consumer (com.morningstar.dp.messaging.common.impl.RunnableConsumer@25364645)
2025-09-12 17:37:01.968 [main] INFO  [] c.m.m.MartKafkaConsumerApplication -- Started MartKafkaConsumerApplication in 20.804 seconds (process running for 21.372)
2025-09-12 17:37:01.970 [main] INFO  [] c.m.m.c.AbstractMessageConsumer -- Starting Kafka message consumer for topic: market_price_enriched_event
2025-09-12 17:37:01.971 [main] INFO  [] c.m.m.c.AbstractMessageConsumer -- Kafka message consumer started successfully
2025-09-12 17:37:21.287 [runnable-consumer-0] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- Trying to close Kafka consumer, RunnableConsumer.isRunning: false
2025-09-12 17:44:25.973 [main] INFO  [] c.m.m.security.AuthInitializer -- [Authentication] Keytab: C:\Users\<USER>\AppData\Local\Temp\service-keytab-17099585441232851368.keytab
2025-09-12 17:44:25.975 [main] INFO  [] c.m.m.security.AuthInitializer -- [Authentication] JAAS conf: C:\Users\<USER>\AppData\Local\Temp\jaas-9674908189911295150.conf
2025-09-12 17:44:25.975 [main] INFO  [] c.m.m.security.AuthInitializer -- [Authentication] krb5 conf: C:\Users\<USER>\AppData\Local\Temp\krb5-10775429392857001160.conf
2025-09-12 17:44:25.978 [main] INFO  [] c.m.m.MartKafkaConsumerApplication -- Starting MartKafkaConsumerApplication using Java 17.0.9 with PID 32216 (C:\Zeng\dataac\msstash\mart-kafka-consumer\target\classes started by wzeng1 in C:\Zeng\dataac\msstash\mart-kafka-consumer)
2025-09-12 17:44:25.978 [main] DEBUG [] c.m.m.MartKafkaConsumerApplication -- Running with Spring Boot v3.5.5, Spring v6.2.10
2025-09-12 17:44:25.979 [main] INFO  [] c.m.m.MartKafkaConsumerApplication -- The following 1 profile is active: "dev"
2025-09-12 17:44:26.270 [main] INFO  [] o.s.d.r.c.RepositoryConfigurationDelegate -- Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-12 17:44:26.272 [main] INFO  [] o.s.d.r.c.RepositoryConfigurationDelegate -- Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-12 17:44:26.297 [main] INFO  [] o.s.d.r.c.RepositoryConfigurationDelegate -- Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces.
2025-09-12 17:44:26.422 [main] INFO  [] c.m.m.config.ExecutorConfig -- Created MarketPrice ExecutorService with 20 threads
2025-09-12 17:44:27.978 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Kafka factory fully initialized with region (aws-us-east-1) and env (dev)
2025-09-12 17:44:27.993 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- [DEPRECATED-Should not be here] - Done setting a system property for serde class (key:serde_pojo_class_name_tag, value:com.morningstar.data.domain.eod.events.MarketPriceEvent)
2025-09-12 17:44:27.995 [main] INFO  [] c.m.d.m.c.s.a.AvroNoSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.eod.events.MarketPriceEvent, codecName: null)
2025-09-12 17:44:28.563 [main] WARN  [] o.a.k.c.consumer.ConsumerConfig -- The configuration 'schema.registry.url' was supplied but isn't a known config.
2025-09-12 17:44:28.574 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 17:44:28.576 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- This generic serde is using (serdePojoClassName: class com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 17:44:28.650 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- About to send an 'adminMessage' to this workflow (clients_connections_details) about this client connection: ProducerRecord(topic=dev_clients_connections_details, partition=null, headers=RecordHeaders(headers = [], isReadOnly = false), key=market_price_list_updated|DPDA_Market_Price_Consumer|CA-JT26LR3, value={"clientId": "DPDA_Market_Price_Consumer", "workflowId": "market_price_list_updated", "connectionDescriptor": {"region": "aws-us-east-1", "env": "dev", "hostname": "CA-JT26LR3", "port": -1, "pid": 32216, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-2.0.0.jar", "version": "kafka-clients-2.0.0.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757713468573", "endDateTime": null, "error": null}, timestamp=null)
2025-09-12 17:44:43.563 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Sent an 'adminMessage' to this workflow (clients_connections_details) about this client connection: ProducerRecord(topic=dev_clients_connections_details, partition=null, headers=RecordHeaders(headers = [], isReadOnly = true), key=market_price_list_updated|DPDA_Market_Price_Consumer|CA-JT26LR3, value={"clientId": "DPDA_Market_Price_Consumer", "workflowId": "market_price_list_updated", "connectionDescriptor": {"region": "aws-us-east-1", "env": "dev", "hostname": "CA-JT26LR3", "port": -1, "pid": 32216, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-2.0.0.jar", "version": "kafka-clients-2.0.0.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757713468573", "endDateTime": null, "error": null}, timestamp=null)
2025-09-12 17:44:44.384 [main] WARN  [] c.m.d.m.common.impl.KafkaFactory -- This method is being deprecated, we should not use it going forward! Please use other overloaded methods. Done building fully configured consumer (org.apache.kafka.clients.consumer.KafkaConsumer@383fddb3). 
Morningstar custom properties:
	{tasks_number_tag=1, serde_pojo_class_name_tag=com.morningstar.data.domain.eod.events.MarketPriceEvent, topic_name_tag=dev_market_price_list_updated}. 
This Consumer Connection Details:
	{"clientId": "DPDA_Market_Price_Consumer", "workflowId": "market_price_list_updated", "connectionDescriptor": {"region": "aws-us-east-1", "env": "dev", "hostname": "CA-JT26LR3", "port": -1, "pid": 32216, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-2.0.0.jar", "version": "kafka-clients-2.0.0.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757713468573", "endDateTime": null, "error": null}
2025-09-12 17:44:44.398 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- workflow market_price_list_updated enable.auto.commit is configured as false
2025-09-12 17:44:44.399 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- Done initialize this 'RunnableConsumer' - Details:
	Workflow: market_price_list_updated
	Overrides: [{enable.auto.commit=false, group.id=dpda_market_price_group, auto.offset.reset=earliest, client.id=DPDA_Market_Price_Consumer}]
2025-09-12 17:44:44.399 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Done building fully configured consumer (com.morningstar.dp.messaging.common.impl.RunnableConsumer@5cb20350)
2025-09-12 17:44:44.401 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- [DEPRECATED-Should not be here] - Done setting a system property for serde class (key:serde_pojo_class_name_tag, value:com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent)
2025-09-12 17:44:44.402 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent, codecName: null)
2025-09-12 17:44:44.405 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- This generic serde is using (serdePojoClassName: class com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent, codecName: null)
2025-09-12 17:44:44.406 [main] WARN  [] c.m.d.m.common.impl.KafkaFactory -- client id DPDA_Market_Price_Consumer already exists, will change it to DPDA_Market_Price_Consumer-0
2025-09-12 17:44:44.412 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 17:44:44.412 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- This generic serde is using (serdePojoClassName: class com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 17:44:44.412 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- About to send an 'adminMessage' to this workflow (clients_connections_details) about this client connection: ProducerRecord(topic=dev_clients_connections_details, partition=null, headers=RecordHeaders(headers = [], isReadOnly = false), key=market_price_enriched_event|DPDA_Market_Price_Consumer-0|CA-JT26LR3, value={"clientId": "DPDA_Market_Price_Consumer-0", "workflowId": "market_price_enriched_event", "connectionDescriptor": {"region": "aws-us-east-1", "env": "dev", "hostname": "CA-JT26LR3", "port": -1, "pid": 32216, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-2.0.0.jar", "version": "kafka-clients-2.0.0.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757713484412", "endDateTime": null, "error": null}, timestamp=null)
2025-09-12 17:44:44.412 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- This is for auditing only so no issue to worry about. Will not produce into the kafka-Plugin admin topic (clients_connections_details) for now.
2025-09-12 17:44:44.413 [main] WARN  [] c.m.d.m.common.impl.KafkaFactory -- This method is being deprecated, we should not use it going forward! Please use other overloaded methods. Done building fully configured consumer (org.apache.kafka.clients.consumer.KafkaConsumer@20d9ef28). 
Morningstar custom properties:
	{tasks_number_tag=1, serde_pojo_class_name_tag=com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent, topic_name_tag=dev_market_price_enriched_event}. 
This Consumer Connection Details:
	{"clientId": "DPDA_Market_Price_Consumer-0", "workflowId": "market_price_enriched_event", "connectionDescriptor": {"region": "aws-us-east-1", "env": "dev", "hostname": "CA-JT26LR3", "port": -1, "pid": 32216, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-2.0.0.jar", "version": "kafka-clients-2.0.0.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757713484412", "endDateTime": null, "error": null}
2025-09-12 17:44:44.413 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- workflow market_price_enriched_event enable.auto.commit is configured as false
2025-09-12 17:44:44.413 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- Done initialize this 'RunnableConsumer' - Details:
	Workflow: market_price_enriched_event
	Overrides: [{enable.auto.commit=false, group.id=dpda_market_price_group, auto.offset.reset=earliest, client.id=DPDA_Market_Price_Consumer}]
2025-09-12 17:44:44.413 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Done building fully configured consumer (com.morningstar.dp.messaging.common.impl.RunnableConsumer@2aa17940)
2025-09-12 17:44:44.775 [main] INFO  [] c.m.m.MartKafkaConsumerApplication -- Started MartKafkaConsumerApplication in 20.199 seconds (process running for 20.742)
2025-09-12 17:44:44.778 [main] INFO  [] c.m.m.c.AbstractMessageConsumer -- Starting Kafka message consumer for topic: market_price_enriched_event
2025-09-12 17:44:44.778 [main] INFO  [] c.m.m.c.AbstractMessageConsumer -- Kafka message consumer started successfully
2025-09-12 17:48:22.848 [runnable-consumer-0] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- Trying to close Kafka consumer, RunnableConsumer.isRunning: false
2025-09-12 17:51:37.939 [main] INFO  [] c.m.m.security.AuthInitializer -- [Authentication] Keytab: C:\Users\<USER>\AppData\Local\Temp\service-keytab-15744685209555878396.keytab
2025-09-12 17:51:37.941 [main] INFO  [] c.m.m.security.AuthInitializer -- [Authentication] JAAS conf: C:\Users\<USER>\AppData\Local\Temp\jaas-4272818353161776906.conf
2025-09-12 17:51:37.941 [main] INFO  [] c.m.m.security.AuthInitializer -- [Authentication] krb5 conf: C:\Users\<USER>\AppData\Local\Temp\krb5-2400524654491516250.conf
2025-09-12 17:51:37.943 [main] INFO  [] c.m.m.MartKafkaConsumerApplication -- Starting MartKafkaConsumerApplication using Java 17.0.9 with PID 37188 (C:\Zeng\dataac\msstash\mart-kafka-consumer\target\classes started by wzeng1 in C:\Zeng\dataac\msstash\mart-kafka-consumer)
2025-09-12 17:51:37.944 [main] DEBUG [] c.m.m.MartKafkaConsumerApplication -- Running with Spring Boot v3.5.5, Spring v6.2.10
2025-09-12 17:51:37.944 [main] INFO  [] c.m.m.MartKafkaConsumerApplication -- The following 1 profile is active: "dev"
2025-09-12 17:51:38.224 [main] INFO  [] o.s.d.r.c.RepositoryConfigurationDelegate -- Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-12 17:51:38.226 [main] INFO  [] o.s.d.r.c.RepositoryConfigurationDelegate -- Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-12 17:51:38.250 [main] INFO  [] o.s.d.r.c.RepositoryConfigurationDelegate -- Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces.
2025-09-12 17:51:38.382 [main] INFO  [] c.m.m.config.ExecutorConfig -- Created MarketPrice ExecutorService with 20 threads
2025-09-12 17:51:39.945 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Kafka factory fully initialized with region (aws-us-east-1) and env (dev)
2025-09-12 17:51:39.957 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- [DEPRECATED-Should not be here] - Done setting a system property for serde class (key:serde_pojo_class_name_tag, value:com.morningstar.data.domain.eod.events.MarketPriceEvent)
2025-09-12 17:51:39.958 [main] INFO  [] c.m.d.m.c.s.a.AvroNoSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.eod.events.MarketPriceEvent, codecName: null)
2025-09-12 17:51:40.587 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 17:51:40.589 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- This generic serde is using (serdePojoClassName: class com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 17:51:40.677 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- About to send an 'adminMessage' to this workflow (clients_connections_details) about this client connection: ProducerRecord(topic=dev_clients_connections_details, partition=null, headers=RecordHeaders(headers = [], isReadOnly = false), key=market_price_list_updated|DPDA_Market_Price_Consumer|CA-JT26LR3, value={"clientId": "DPDA_Market_Price_Consumer", "workflowId": "market_price_list_updated", "connectionDescriptor": {"region": "aws-us-east-1", "env": "dev", "hostname": "CA-JT26LR3", "port": -1, "pid": 37188, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757713900585", "endDateTime": null, "error": null}, timestamp=null)
2025-09-12 17:51:50.054 [kafka-producer-network-thread | producer-1] WARN  [] o.apache.kafka.clients.NetworkClient -- [Producer clientId=producer-1] Bootstrap broker lin-0af82a2f.mstarext.com:6667 (id: -3 rack: null) disconnected
2025-09-12 17:51:55.767 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Sent an 'adminMessage' to this workflow (clients_connections_details) about this client connection: ProducerRecord(topic=dev_clients_connections_details, partition=null, headers=RecordHeaders(headers = [], isReadOnly = true), key=market_price_list_updated|DPDA_Market_Price_Consumer|CA-JT26LR3, value={"clientId": "DPDA_Market_Price_Consumer", "workflowId": "market_price_list_updated", "connectionDescriptor": {"region": "aws-us-east-1", "env": "dev", "hostname": "CA-JT26LR3", "port": -1, "pid": 37188, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757713900585", "endDateTime": null, "error": null}, timestamp=null)
2025-09-12 17:51:56.513 [kafka-producer-network-thread | producer-1] ERROR [] o.a.k.c.producer.internals.Sender -- [Producer clientId=producer-1] Aborting producer batches due to fatal error
org.apache.kafka.common.errors.ClusterAuthorizationException: Cluster authorization failed.
2025-09-12 17:51:56.526 [main] WARN  [] c.m.d.m.common.impl.KafkaFactory -- This method is being deprecated, we should not use it going forward! Please use other overloaded methods. Done building fully configured consumer (org.apache.kafka.clients.consumer.KafkaConsumer@4086b547). 
Morningstar custom properties:
	{tasks_number_tag=1, serde_pojo_class_name_tag=com.morningstar.data.domain.eod.events.MarketPriceEvent, topic_name_tag=dev_market_price_list_updated}. 
This Consumer Connection Details:
	{"clientId": "DPDA_Market_Price_Consumer", "workflowId": "market_price_list_updated", "connectionDescriptor": {"region": "aws-us-east-1", "env": "dev", "hostname": "CA-JT26LR3", "port": -1, "pid": 37188, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757713900585", "endDateTime": null, "error": null}
2025-09-12 17:51:56.543 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- workflow market_price_list_updated enable.auto.commit is configured as false
2025-09-12 17:51:56.544 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- Done initialize this 'RunnableConsumer' - Details:
	Workflow: market_price_list_updated
	Overrides: [{enable.auto.commit=false, group.id=dpda_market_price_group, auto.offset.reset=earliest, client.id=DPDA_Market_Price_Consumer}]
2025-09-12 17:51:56.544 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Done building fully configured consumer (com.morningstar.dp.messaging.common.impl.RunnableConsumer@5938041b)
2025-09-12 17:51:56.546 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- [DEPRECATED-Should not be here] - Done setting a system property for serde class (key:serde_pojo_class_name_tag, value:com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent)
2025-09-12 17:51:56.546 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent, codecName: null)
2025-09-12 17:51:56.551 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- This generic serde is using (serdePojoClassName: class com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent, codecName: null)
2025-09-12 17:51:56.551 [main] WARN  [] c.m.d.m.common.impl.KafkaFactory -- client id DPDA_Market_Price_Consumer already exists, will change it to DPDA_Market_Price_Consumer-0
2025-09-12 17:51:56.557 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 17:51:56.557 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- This generic serde is using (serdePojoClassName: class com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 17:51:56.557 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- About to send an 'adminMessage' to this workflow (clients_connections_details) about this client connection: ProducerRecord(topic=dev_clients_connections_details, partition=null, headers=RecordHeaders(headers = [], isReadOnly = false), key=market_price_enriched_event|DPDA_Market_Price_Consumer-0|CA-JT26LR3, value={"clientId": "DPDA_Market_Price_Consumer-0", "workflowId": "market_price_enriched_event", "connectionDescriptor": {"region": "aws-us-east-1", "env": "dev", "hostname": "CA-JT26LR3", "port": -1, "pid": 37188, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757713916557", "endDateTime": null, "error": null}, timestamp=null)
2025-09-12 17:51:56.558 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- This is for auditing only so no issue to worry about. Will not produce into the kafka-Plugin admin topic (clients_connections_details) for now.
2025-09-12 17:51:56.558 [main] WARN  [] c.m.d.m.common.impl.KafkaFactory -- This method is being deprecated, we should not use it going forward! Please use other overloaded methods. Done building fully configured consumer (org.apache.kafka.clients.consumer.KafkaConsumer@54c252a4). 
Morningstar custom properties:
	{tasks_number_tag=1, serde_pojo_class_name_tag=com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent, topic_name_tag=dev_market_price_enriched_event}. 
This Consumer Connection Details:
	{"clientId": "DPDA_Market_Price_Consumer-0", "workflowId": "market_price_enriched_event", "connectionDescriptor": {"region": "aws-us-east-1", "env": "dev", "hostname": "CA-JT26LR3", "port": -1, "pid": 37188, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757713916557", "endDateTime": null, "error": null}
2025-09-12 17:51:56.558 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- workflow market_price_enriched_event enable.auto.commit is configured as false
2025-09-12 17:51:56.558 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- Done initialize this 'RunnableConsumer' - Details:
	Workflow: market_price_enriched_event
	Overrides: [{enable.auto.commit=false, group.id=dpda_market_price_group, auto.offset.reset=earliest, client.id=DPDA_Market_Price_Consumer}]
2025-09-12 17:51:56.558 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Done building fully configured consumer (com.morningstar.dp.messaging.common.impl.RunnableConsumer@5ac906d1)
2025-09-12 17:51:56.921 [main] INFO  [] c.m.m.MartKafkaConsumerApplication -- Started MartKafkaConsumerApplication in 20.74 seconds (process running for 21.5)
2025-09-12 17:51:56.923 [main] INFO  [] c.m.m.c.AbstractMessageConsumer -- Starting Kafka message consumer for topic: market_price_enriched_event
2025-09-12 17:51:56.923 [main] INFO  [] c.m.m.c.AbstractMessageConsumer -- Kafka message consumer started successfully
2025-09-12 17:53:13.592 [runnable-consumer-0] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- Trying to close Kafka consumer, RunnableConsumer.isRunning: false
2025-09-12 18:25:05.742 [main] ERROR [] c.m.m.security.AuthInitializer -- [Authentication] Error authenticating
software.amazon.awssdk.core.exception.SdkClientException: Unable to load credentials from any of the providers in the chain AwsCredentialsProviderChain(credentialsProviders=[SystemPropertyCredentialsProvider(), EnvironmentVariableCredentialsProvider(), WebIdentityTokenCredentialsProvider(), ProfileCredentialsProvider(profileName=default, profileFile=ProfileFile(profilesAndSectionsMap=[{default=Profile(name=default, properties=[sso_region, sso_start_url, sso_role_name, region, sso_account_id]), 619071339464_mstar-operator=Profile(name=619071339464_mstar-operator, properties=[aws_access_key_id, aws_session_token, aws_secret_access_key]), 270863951168_mstar-operator=Profile(name=270863951168_mstar-operator, properties=[aws_access_key_id, aws_session_token, aws_secret_access_key]), 921072466220_mstar-readonly=Profile(name=921072466220_mstar-readonly, properties=[aws_access_key_id, aws_session_token, aws_secret_access_key])}, {}])), ContainerCredentialsProvider(), InstanceProfileCredentialsProvider()]) : [SystemPropertyCredentialsProvider(): Unable to load credentials from system settings. Access key must be specified either via environment variable (AWS_ACCESS_KEY_ID) or system property (aws.accessKeyId)., EnvironmentVariableCredentialsProvider(): Unable to load credentials from system settings. Access key must be specified either via environment variable (AWS_ACCESS_KEY_ID) or system property (aws.accessKeyId)., WebIdentityTokenCredentialsProvider(): Either the environment variable AWS_WEB_IDENTITY_TOKEN_FILE or the javaproperty aws.webIdentityTokenFile must be set., ProfileCredentialsProvider(profileName=default, profileFile=ProfileFile(profilesAndSectionsMap=[{default=Profile(name=default, properties=[sso_region, sso_start_url, sso_role_name, region, sso_account_id]), 619071339464_mstar-operator=Profile(name=619071339464_mstar-operator, properties=[aws_access_key_id, aws_session_token, aws_secret_access_key]), 270863951168_mstar-operator=Profile(name=270863951168_mstar-operator, properties=[aws_access_key_id, aws_session_token, aws_secret_access_key]), 921072466220_mstar-readonly=Profile(name=921072466220_mstar-readonly, properties=[aws_access_key_id, aws_session_token, aws_secret_access_key])}, {}])): To use Sso related properties in the 'default' profile, the 'sso' service module must be on the class path., ContainerCredentialsProvider(): Cannot fetch credentials from container - neither AWS_CONTAINER_CREDENTIALS_FULL_URI or AWS_CONTAINER_CREDENTIALS_RELATIVE_URI environment variables are set., InstanceProfileCredentialsProvider(): Failed to load credentials from IMDS.]
	at software.amazon.awssdk.core.exception.SdkClientException$BuilderImpl.build(SdkClientException.java:111)
	at software.amazon.awssdk.auth.credentials.AwsCredentialsProviderChain.resolveCredentials(AwsCredentialsProviderChain.java:117)
	at software.amazon.awssdk.auth.credentials.internal.LazyAwsCredentialsProvider.resolveCredentials(LazyAwsCredentialsProvider.java:45)
	at software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider.resolveCredentials(DefaultCredentialsProvider.java:126)
	at software.amazon.awssdk.core.internal.util.MetricUtils.measureDuration(MetricUtils.java:50)
	at software.amazon.awssdk.awscore.internal.authcontext.AwsCredentialsAuthorizationStrategy.resolveCredentials(AwsCredentialsAuthorizationStrategy.java:100)
	at software.amazon.awssdk.awscore.internal.authcontext.AwsCredentialsAuthorizationStrategy.addCredentialsToExecutionAttributes(AwsCredentialsAuthorizationStrategy.java:77)
	at software.amazon.awssdk.awscore.internal.AwsExecutionContextBuilder.invokeInterceptorsAndCreateExecutionContext(AwsExecutionContextBuilder.java:123)
	at software.amazon.awssdk.awscore.client.handler.AwsSyncClientHandler.invokeInterceptorsAndCreateExecutionContext(AwsSyncClientHandler.java:69)
	at software.amazon.awssdk.core.internal.handler.BaseSyncClientHandler.lambda$execute$1(BaseSyncClientHandler.java:78)
	at software.amazon.awssdk.core.internal.handler.BaseSyncClientHandler.measureApiCallSuccess(BaseSyncClientHandler.java:179)
	at software.amazon.awssdk.core.internal.handler.BaseSyncClientHandler.execute(BaseSyncClientHandler.java:76)
	at software.amazon.awssdk.core.client.handler.SdkSyncClientHandler.execute(SdkSyncClientHandler.java:45)
	at software.amazon.awssdk.awscore.client.handler.AwsSyncClientHandler.execute(AwsSyncClientHandler.java:56)
	at software.amazon.awssdk.services.ssm.DefaultSsmClient.getParameter(DefaultSsmClient.java:8698)
	at com.morningstar.martkafkaconsumer.security.AuthInitializer.getParameterFromSsm(AuthInitializer.java:50)
	at com.morningstar.martkafkaconsumer.security.AuthInitializer.initialize(AuthInitializer.java:26)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:612)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:383)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:317)
	at com.morningstar.martkafkaconsumer.MartKafkaConsumerApplication.main(MartKafkaConsumerApplication.java:13)
2025-09-12 18:25:05.761 [main] ERROR [] o.s.boot.SpringApplication -- Application run failed
java.lang.RuntimeException: Failed to authenticate
	at com.morningstar.martkafkaconsumer.security.AuthInitializer.initialize(AuthInitializer.java:43)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:612)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:383)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:317)
	at com.morningstar.martkafkaconsumer.MartKafkaConsumerApplication.main(MartKafkaConsumerApplication.java:13)
Caused by: software.amazon.awssdk.core.exception.SdkClientException: Unable to load credentials from any of the providers in the chain AwsCredentialsProviderChain(credentialsProviders=[SystemPropertyCredentialsProvider(), EnvironmentVariableCredentialsProvider(), WebIdentityTokenCredentialsProvider(), ProfileCredentialsProvider(profileName=default, profileFile=ProfileFile(profilesAndSectionsMap=[{default=Profile(name=default, properties=[sso_region, sso_start_url, sso_role_name, region, sso_account_id]), 619071339464_mstar-operator=Profile(name=619071339464_mstar-operator, properties=[aws_access_key_id, aws_session_token, aws_secret_access_key]), 270863951168_mstar-operator=Profile(name=270863951168_mstar-operator, properties=[aws_access_key_id, aws_session_token, aws_secret_access_key]), 921072466220_mstar-readonly=Profile(name=921072466220_mstar-readonly, properties=[aws_access_key_id, aws_session_token, aws_secret_access_key])}, {}])), ContainerCredentialsProvider(), InstanceProfileCredentialsProvider()]) : [SystemPropertyCredentialsProvider(): Unable to load credentials from system settings. Access key must be specified either via environment variable (AWS_ACCESS_KEY_ID) or system property (aws.accessKeyId)., EnvironmentVariableCredentialsProvider(): Unable to load credentials from system settings. Access key must be specified either via environment variable (AWS_ACCESS_KEY_ID) or system property (aws.accessKeyId)., WebIdentityTokenCredentialsProvider(): Either the environment variable AWS_WEB_IDENTITY_TOKEN_FILE or the javaproperty aws.webIdentityTokenFile must be set., ProfileCredentialsProvider(profileName=default, profileFile=ProfileFile(profilesAndSectionsMap=[{default=Profile(name=default, properties=[sso_region, sso_start_url, sso_role_name, region, sso_account_id]), 619071339464_mstar-operator=Profile(name=619071339464_mstar-operator, properties=[aws_access_key_id, aws_session_token, aws_secret_access_key]), 270863951168_mstar-operator=Profile(name=270863951168_mstar-operator, properties=[aws_access_key_id, aws_session_token, aws_secret_access_key]), 921072466220_mstar-readonly=Profile(name=921072466220_mstar-readonly, properties=[aws_access_key_id, aws_session_token, aws_secret_access_key])}, {}])): To use Sso related properties in the 'default' profile, the 'sso' service module must be on the class path., ContainerCredentialsProvider(): Cannot fetch credentials from container - neither AWS_CONTAINER_CREDENTIALS_FULL_URI or AWS_CONTAINER_CREDENTIALS_RELATIVE_URI environment variables are set., InstanceProfileCredentialsProvider(): Failed to load credentials from IMDS.]
	at software.amazon.awssdk.core.exception.SdkClientException$BuilderImpl.build(SdkClientException.java:111)
	at software.amazon.awssdk.auth.credentials.AwsCredentialsProviderChain.resolveCredentials(AwsCredentialsProviderChain.java:117)
	at software.amazon.awssdk.auth.credentials.internal.LazyAwsCredentialsProvider.resolveCredentials(LazyAwsCredentialsProvider.java:45)
	at software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider.resolveCredentials(DefaultCredentialsProvider.java:126)
	at software.amazon.awssdk.core.internal.util.MetricUtils.measureDuration(MetricUtils.java:50)
	at software.amazon.awssdk.awscore.internal.authcontext.AwsCredentialsAuthorizationStrategy.resolveCredentials(AwsCredentialsAuthorizationStrategy.java:100)
	at software.amazon.awssdk.awscore.internal.authcontext.AwsCredentialsAuthorizationStrategy.addCredentialsToExecutionAttributes(AwsCredentialsAuthorizationStrategy.java:77)
	at software.amazon.awssdk.awscore.internal.AwsExecutionContextBuilder.invokeInterceptorsAndCreateExecutionContext(AwsExecutionContextBuilder.java:123)
	at software.amazon.awssdk.awscore.client.handler.AwsSyncClientHandler.invokeInterceptorsAndCreateExecutionContext(AwsSyncClientHandler.java:69)
	at software.amazon.awssdk.core.internal.handler.BaseSyncClientHandler.lambda$execute$1(BaseSyncClientHandler.java:78)
	at software.amazon.awssdk.core.internal.handler.BaseSyncClientHandler.measureApiCallSuccess(BaseSyncClientHandler.java:179)
	at software.amazon.awssdk.core.internal.handler.BaseSyncClientHandler.execute(BaseSyncClientHandler.java:76)
	at software.amazon.awssdk.core.client.handler.SdkSyncClientHandler.execute(SdkSyncClientHandler.java:45)
	at software.amazon.awssdk.awscore.client.handler.AwsSyncClientHandler.execute(AwsSyncClientHandler.java:56)
	at software.amazon.awssdk.services.ssm.DefaultSsmClient.getParameter(DefaultSsmClient.java:8698)
	at com.morningstar.martkafkaconsumer.security.AuthInitializer.getParameterFromSsm(AuthInitializer.java:50)
	at com.morningstar.martkafkaconsumer.security.AuthInitializer.initialize(AuthInitializer.java:26)
	... 4 common frames omitted
2025-09-12 18:25:56.521 [main] INFO  [] c.m.m.security.AuthInitializer -- [Authentication] Keytab: C:\Users\<USER>\AppData\Local\Temp\service-keytab-13260567131546215157.keytab
2025-09-12 18:25:56.522 [main] INFO  [] c.m.m.security.AuthInitializer -- [Authentication] JAAS conf: C:\Users\<USER>\AppData\Local\Temp\jaas-16295952350454496772.conf
2025-09-12 18:25:56.522 [main] INFO  [] c.m.m.security.AuthInitializer -- [Authentication] krb5 conf: C:\Users\<USER>\AppData\Local\Temp\krb5-9778812602486724526.conf
2025-09-12 18:25:56.525 [main] INFO  [] c.m.m.MartKafkaConsumerApplication -- Starting MartKafkaConsumerApplication using Java 17.0.9 with PID 1200 (C:\Zeng\dataac\msstash\mart-kafka-consumer\target\classes started by wzeng1 in C:\Zeng\dataac\msstash\mart-kafka-consumer)
2025-09-12 18:25:56.525 [main] DEBUG [] c.m.m.MartKafkaConsumerApplication -- Running with Spring Boot v3.5.5, Spring v6.2.10
2025-09-12 18:25:56.525 [main] INFO  [] c.m.m.MartKafkaConsumerApplication -- The following 1 profile is active: "dev"
2025-09-12 18:25:56.821 [main] INFO  [] o.s.d.r.c.RepositoryConfigurationDelegate -- Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-12 18:25:56.823 [main] INFO  [] o.s.d.r.c.RepositoryConfigurationDelegate -- Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-12 18:25:56.845 [main] INFO  [] o.s.d.r.c.RepositoryConfigurationDelegate -- Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces.
2025-09-12 18:25:56.976 [main] INFO  [] c.m.m.config.ExecutorConfig -- Created MarketPrice ExecutorService with 20 threads
2025-09-12 18:25:58.459 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Kafka factory fully initialized with region (aws-us-east-1) and env (dev)
2025-09-12 18:25:58.471 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- [DEPRECATED-Should not be here] - Done setting a system property for serde class (key:serde_pojo_class_name_tag, value:com.morningstar.data.domain.eod.events.MarketPriceEvent)
2025-09-12 18:25:58.473 [main] INFO  [] c.m.d.m.c.s.a.AvroNoSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.eod.events.MarketPriceEvent, codecName: null)
2025-09-12 18:25:59.353 [main] WARN  [] o.a.k.c.consumer.ConsumerConfig -- The configuration 'schema.registry.url' was supplied but isn't a known config.
2025-09-12 18:25:59.364 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 18:25:59.365 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- This generic serde is using (serdePojoClassName: class com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 18:25:59.444 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- About to send an 'adminMessage' to this workflow (clients_connections_details) about this client connection: ProducerRecord(topic=dev_clients_connections_details, partition=null, headers=RecordHeaders(headers = [], isReadOnly = false), key=market_price_list_updated|DPDA_Market_Price_Consumer|CA-JT26LR3, value={"clientId": "DPDA_Market_Price_Consumer", "workflowId": "market_price_list_updated", "connectionDescriptor": {"region": "aws-us-east-1", "env": "dev", "hostname": "CA-JT26LR3", "port": -1, "pid": 1200, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-2.0.0.jar", "version": "kafka-clients-2.0.0.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757715959362", "endDateTime": null, "error": null}, timestamp=null)
2025-09-12 18:26:14.408 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Sent an 'adminMessage' to this workflow (clients_connections_details) about this client connection: ProducerRecord(topic=dev_clients_connections_details, partition=null, headers=RecordHeaders(headers = [], isReadOnly = true), key=market_price_list_updated|DPDA_Market_Price_Consumer|CA-JT26LR3, value={"clientId": "DPDA_Market_Price_Consumer", "workflowId": "market_price_list_updated", "connectionDescriptor": {"region": "aws-us-east-1", "env": "dev", "hostname": "CA-JT26LR3", "port": -1, "pid": 1200, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-2.0.0.jar", "version": "kafka-clients-2.0.0.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757715959362", "endDateTime": null, "error": null}, timestamp=null)
2025-09-12 18:26:15.097 [main] WARN  [] c.m.d.m.common.impl.KafkaFactory -- This method is being deprecated, we should not use it going forward! Please use other overloaded methods. Done building fully configured consumer (org.apache.kafka.clients.consumer.KafkaConsumer@383fddb3). 
Morningstar custom properties:
	{tasks_number_tag=1, serde_pojo_class_name_tag=com.morningstar.data.domain.eod.events.MarketPriceEvent, topic_name_tag=dev_market_price_list_updated}. 
This Consumer Connection Details:
	{"clientId": "DPDA_Market_Price_Consumer", "workflowId": "market_price_list_updated", "connectionDescriptor": {"region": "aws-us-east-1", "env": "dev", "hostname": "CA-JT26LR3", "port": -1, "pid": 1200, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-2.0.0.jar", "version": "kafka-clients-2.0.0.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757715959362", "endDateTime": null, "error": null}
2025-09-12 18:26:15.119 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- workflow market_price_list_updated enable.auto.commit is configured as false
2025-09-12 18:26:15.120 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- Done initialize this 'RunnableConsumer' - Details:
	Workflow: market_price_list_updated
	Overrides: [{enable.auto.commit=false, group.id=dpda_market_price_group, auto.offset.reset=earliest, client.id=DPDA_Market_Price_Consumer}]
2025-09-12 18:26:15.120 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Done building fully configured consumer (com.morningstar.dp.messaging.common.impl.RunnableConsumer@5cb20350)
2025-09-12 18:26:15.122 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- [DEPRECATED-Should not be here] - Done setting a system property for serde class (key:serde_pojo_class_name_tag, value:com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent)
2025-09-12 18:26:15.122 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent, codecName: null)
2025-09-12 18:26:15.128 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- This generic serde is using (serdePojoClassName: class com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent, codecName: null)
2025-09-12 18:26:15.128 [main] WARN  [] c.m.d.m.common.impl.KafkaFactory -- client id DPDA_Market_Price_Consumer already exists, will change it to DPDA_Market_Price_Consumer-0
2025-09-12 18:26:15.134 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 18:26:15.134 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- This generic serde is using (serdePojoClassName: class com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 18:26:15.135 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- About to send an 'adminMessage' to this workflow (clients_connections_details) about this client connection: ProducerRecord(topic=dev_clients_connections_details, partition=null, headers=RecordHeaders(headers = [], isReadOnly = false), key=market_price_enriched_event|DPDA_Market_Price_Consumer-0|CA-JT26LR3, value={"clientId": "DPDA_Market_Price_Consumer-0", "workflowId": "market_price_enriched_event", "connectionDescriptor": {"region": "aws-us-east-1", "env": "dev", "hostname": "CA-JT26LR3", "port": -1, "pid": 1200, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-2.0.0.jar", "version": "kafka-clients-2.0.0.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757715975134", "endDateTime": null, "error": null}, timestamp=null)
2025-09-12 18:26:15.135 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- This is for auditing only so no issue to worry about. Will not produce into the kafka-Plugin admin topic (clients_connections_details) for now.
2025-09-12 18:26:15.135 [main] WARN  [] c.m.d.m.common.impl.KafkaFactory -- This method is being deprecated, we should not use it going forward! Please use other overloaded methods. Done building fully configured consumer (org.apache.kafka.clients.consumer.KafkaConsumer@20d9ef28). 
Morningstar custom properties:
	{tasks_number_tag=1, serde_pojo_class_name_tag=com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent, topic_name_tag=dev_market_price_enriched_event}. 
This Consumer Connection Details:
	{"clientId": "DPDA_Market_Price_Consumer-0", "workflowId": "market_price_enriched_event", "connectionDescriptor": {"region": "aws-us-east-1", "env": "dev", "hostname": "CA-JT26LR3", "port": -1, "pid": 1200, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-2.0.0.jar", "version": "kafka-clients-2.0.0.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757715975134", "endDateTime": null, "error": null}
2025-09-12 18:26:15.135 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- workflow market_price_enriched_event enable.auto.commit is configured as false
2025-09-12 18:26:15.135 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- Done initialize this 'RunnableConsumer' - Details:
	Workflow: market_price_enriched_event
	Overrides: [{enable.auto.commit=false, group.id=dpda_market_price_group, auto.offset.reset=earliest, client.id=DPDA_Market_Price_Consumer}]
2025-09-12 18:26:15.135 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Done building fully configured consumer (com.morningstar.dp.messaging.common.impl.RunnableConsumer@2aa17940)
2025-09-12 18:26:15.499 [main] INFO  [] c.m.m.MartKafkaConsumerApplication -- Started MartKafkaConsumerApplication in 20.347 seconds (process running for 20.859)
2025-09-12 18:26:15.501 [main] INFO  [] c.m.m.c.AbstractMessageConsumer -- Starting Kafka message consumer for topic: market_price_enriched_event
2025-09-12 18:26:15.502 [main] INFO  [] c.m.m.c.AbstractMessageConsumer -- Kafka message consumer started successfully
2025-09-12 18:28:12.823 [runnable-consumer-0] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- Trying to close Kafka consumer, RunnableConsumer.isRunning: false
2025-09-12 18:40:57.291 [main] INFO  [] c.m.m.security.AuthInitializer -- [Authentication] Keytab: C:\Users\<USER>\AppData\Local\Temp\service-keytab-227627129517289110.keytab
2025-09-12 18:40:57.292 [main] INFO  [] c.m.m.security.AuthInitializer -- [Authentication] JAAS conf: C:\Users\<USER>\AppData\Local\Temp\jaas-5328414016303512784.conf
2025-09-12 18:40:57.293 [main] INFO  [] c.m.m.security.AuthInitializer -- [Authentication] krb5 conf: C:\Users\<USER>\AppData\Local\Temp\krb5-8650631715361900129.conf
2025-09-12 18:40:57.295 [main] INFO  [] c.m.m.MartKafkaConsumerApplication -- Starting MartKafkaConsumerApplication using Java 17.0.9 with PID 2076 (C:\Zeng\dataac\msstash\mart-kafka-consumer\target\classes started by wzeng1 in C:\Zeng\dataac\msstash\mart-kafka-consumer)
2025-09-12 18:40:57.295 [main] DEBUG [] c.m.m.MartKafkaConsumerApplication -- Running with Spring Boot v3.5.5, Spring v6.2.10
2025-09-12 18:40:57.296 [main] INFO  [] c.m.m.MartKafkaConsumerApplication -- The following 1 profile is active: "dev"
2025-09-12 18:40:57.578 [main] INFO  [] o.s.d.r.c.RepositoryConfigurationDelegate -- Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-12 18:40:57.579 [main] INFO  [] o.s.d.r.c.RepositoryConfigurationDelegate -- Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-12 18:40:57.603 [main] INFO  [] o.s.d.r.c.RepositoryConfigurationDelegate -- Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
2025-09-12 18:40:57.735 [main] INFO  [] c.m.m.config.ExecutorConfig -- Created MarketPrice ExecutorService with 20 threads
2025-09-12 18:40:59.433 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Kafka factory fully initialized with region (aws-us-east-1) and env (dev)
2025-09-12 18:40:59.445 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- [DEPRECATED-Should not be here] - Done setting a system property for serde class (key:serde_pojo_class_name_tag, value:com.morningstar.data.domain.eod.events.MarketPriceEvent)
2025-09-12 18:40:59.448 [main] INFO  [] c.m.d.m.c.s.a.AvroNoSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.eod.events.MarketPriceEvent, codecName: null)
2025-09-12 18:41:00.105 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 18:41:00.106 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- This generic serde is using (serdePojoClassName: class com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 18:41:00.199 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- About to send an 'adminMessage' to this workflow (clients_connections_details) about this client connection: ProducerRecord(topic=dev_clients_connections_details, partition=null, headers=RecordHeaders(headers = [], isReadOnly = false), key=market_price_list_updated|DPDA_Market_Price_Consumer|CA-JT26LR3, value={"clientId": "DPDA_Market_Price_Consumer", "workflowId": "market_price_list_updated", "connectionDescriptor": {"region": "aws-us-east-1", "env": "dev", "hostname": "CA-JT26LR3", "port": -1, "pid": 2076, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757716860103", "endDateTime": null, "error": null}, timestamp=null)
2025-09-12 18:41:10.529 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Sent an 'adminMessage' to this workflow (clients_connections_details) about this client connection: ProducerRecord(topic=dev_clients_connections_details, partition=null, headers=RecordHeaders(headers = [], isReadOnly = true), key=market_price_list_updated|DPDA_Market_Price_Consumer|CA-JT26LR3, value={"clientId": "DPDA_Market_Price_Consumer", "workflowId": "market_price_list_updated", "connectionDescriptor": {"region": "aws-us-east-1", "env": "dev", "hostname": "CA-JT26LR3", "port": -1, "pid": 2076, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757716860103", "endDateTime": null, "error": null}, timestamp=null)
2025-09-12 18:41:10.536 [kafka-producer-network-thread | producer-1] ERROR [] o.a.k.c.producer.internals.Sender -- [Producer clientId=producer-1] Aborting producer batches due to fatal error
org.apache.kafka.common.errors.ClusterAuthorizationException: Cluster authorization failed.
2025-09-12 18:41:10.543 [main] WARN  [] c.m.d.m.common.impl.KafkaFactory -- This method is being deprecated, we should not use it going forward! Please use other overloaded methods. Done building fully configured consumer (org.apache.kafka.clients.consumer.KafkaConsumer@4086b547). 
Morningstar custom properties:
	{tasks_number_tag=1, serde_pojo_class_name_tag=com.morningstar.data.domain.eod.events.MarketPriceEvent, topic_name_tag=dev_market_price_list_updated}. 
This Consumer Connection Details:
	{"clientId": "DPDA_Market_Price_Consumer", "workflowId": "market_price_list_updated", "connectionDescriptor": {"region": "aws-us-east-1", "env": "dev", "hostname": "CA-JT26LR3", "port": -1, "pid": 2076, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757716860103", "endDateTime": null, "error": null}
2025-09-12 18:41:10.552 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- workflow market_price_list_updated enable.auto.commit is configured as false
2025-09-12 18:41:10.553 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- Done initialize this 'RunnableConsumer' - Details:
	Workflow: market_price_list_updated
	Overrides: [{enable.auto.commit=false, group.id=dpda_market_price_group, auto.offset.reset=earliest, client.id=DPDA_Market_Price_Consumer}]
2025-09-12 18:41:10.553 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Done building fully configured consumer (com.morningstar.dp.messaging.common.impl.RunnableConsumer@5938041b)
2025-09-12 18:41:10.555 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- [DEPRECATED-Should not be here] - Done setting a system property for serde class (key:serde_pojo_class_name_tag, value:com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent)
2025-09-12 18:41:10.556 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent, codecName: null)
2025-09-12 18:41:10.558 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- This generic serde is using (serdePojoClassName: class com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent, codecName: null)
2025-09-12 18:41:10.559 [main] WARN  [] c.m.d.m.common.impl.KafkaFactory -- client id DPDA_Market_Price_Consumer already exists, will change it to DPDA_Market_Price_Consumer-0
2025-09-12 18:41:10.565 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 18:41:10.566 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- This generic serde is using (serdePojoClassName: class com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 18:41:10.566 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- About to send an 'adminMessage' to this workflow (clients_connections_details) about this client connection: ProducerRecord(topic=dev_clients_connections_details, partition=null, headers=RecordHeaders(headers = [], isReadOnly = false), key=market_price_enriched_event|DPDA_Market_Price_Consumer-0|CA-JT26LR3, value={"clientId": "DPDA_Market_Price_Consumer-0", "workflowId": "market_price_enriched_event", "connectionDescriptor": {"region": "aws-us-east-1", "env": "dev", "hostname": "CA-JT26LR3", "port": -1, "pid": 2076, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757716870565", "endDateTime": null, "error": null}, timestamp=null)
2025-09-12 18:41:10.566 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- This is for auditing only so no issue to worry about. Will not produce into the kafka-Plugin admin topic (clients_connections_details) for now.
2025-09-12 18:41:10.566 [main] WARN  [] c.m.d.m.common.impl.KafkaFactory -- This method is being deprecated, we should not use it going forward! Please use other overloaded methods. Done building fully configured consumer (org.apache.kafka.clients.consumer.KafkaConsumer@54c252a4). 
Morningstar custom properties:
	{tasks_number_tag=1, serde_pojo_class_name_tag=com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent, topic_name_tag=dev_market_price_enriched_event}. 
This Consumer Connection Details:
	{"clientId": "DPDA_Market_Price_Consumer-0", "workflowId": "market_price_enriched_event", "connectionDescriptor": {"region": "aws-us-east-1", "env": "dev", "hostname": "CA-JT26LR3", "port": -1, "pid": 2076, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757716870565", "endDateTime": null, "error": null}
2025-09-12 18:41:10.566 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- workflow market_price_enriched_event enable.auto.commit is configured as false
2025-09-12 18:41:10.567 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- Done initialize this 'RunnableConsumer' - Details:
	Workflow: market_price_enriched_event
	Overrides: [{enable.auto.commit=false, group.id=dpda_market_price_group, auto.offset.reset=earliest, client.id=DPDA_Market_Price_Consumer}]
2025-09-12 18:41:10.567 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Done building fully configured consumer (com.morningstar.dp.messaging.common.impl.RunnableConsumer@5ac906d1)
2025-09-12 18:41:10.934 [main] INFO  [] c.m.m.MartKafkaConsumerApplication -- Started MartKafkaConsumerApplication in 15.056 seconds (process running for 15.628)
2025-09-12 18:41:10.936 [main] INFO  [] c.m.m.c.AbstractMessageConsumer -- Starting Kafka message consumer for topic: market_price_enriched_event
2025-09-12 18:41:10.936 [main] INFO  [] c.m.m.c.AbstractMessageConsumer -- Kafka message consumer started successfully
2025-09-12 18:41:34.683 [runnable-consumer-0] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- Trying to close Kafka consumer, RunnableConsumer.isRunning: false
2025-09-12 18:42:13.552 [main] INFO  [] c.m.m.security.AuthInitializer -- [Authentication] Keytab: C:\Users\<USER>\AppData\Local\Temp\service-keytab-17873438456001912477.keytab
2025-09-12 18:42:13.554 [main] INFO  [] c.m.m.security.AuthInitializer -- [Authentication] JAAS conf: C:\Users\<USER>\AppData\Local\Temp\jaas-1666988848620818641.conf
2025-09-12 18:42:13.554 [main] INFO  [] c.m.m.security.AuthInitializer -- [Authentication] krb5 conf: C:\Users\<USER>\AppData\Local\Temp\krb5-17063496188860545045.conf
2025-09-12 18:42:13.556 [main] INFO  [] c.m.m.MartKafkaConsumerApplication -- Starting MartKafkaConsumerApplication using Java 17.0.9 with PID 36628 (C:\Zeng\dataac\msstash\mart-kafka-consumer\target\classes started by wzeng1 in C:\Zeng\dataac\msstash\mart-kafka-consumer)
2025-09-12 18:42:13.556 [main] DEBUG [] c.m.m.MartKafkaConsumerApplication -- Running with Spring Boot v3.5.5, Spring v6.2.10
2025-09-12 18:42:13.557 [main] INFO  [] c.m.m.MartKafkaConsumerApplication -- The following 1 profile is active: "dev"
2025-09-12 18:42:13.833 [main] INFO  [] o.s.d.r.c.RepositoryConfigurationDelegate -- Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-12 18:42:13.835 [main] INFO  [] o.s.d.r.c.RepositoryConfigurationDelegate -- Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-12 18:42:13.861 [main] INFO  [] o.s.d.r.c.RepositoryConfigurationDelegate -- Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
2025-09-12 18:42:13.986 [main] INFO  [] c.m.m.config.ExecutorConfig -- Created MarketPrice ExecutorService with 20 threads
2025-09-12 18:42:15.362 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Kafka factory fully initialized with region (aws-us-east-1) and env (devcloud)
2025-09-12 18:42:15.374 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- [DEPRECATED-Should not be here] - Done setting a system property for serde class (key:serde_pojo_class_name_tag, value:com.morningstar.data.domain.eod.events.MarketPriceEvent)
2025-09-12 18:42:15.376 [main] INFO  [] c.m.d.m.c.s.a.AvroNoSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.eod.events.MarketPriceEvent, codecName: null)
2025-09-12 18:42:15.647 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 18:42:15.649 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- This generic serde is using (serdePojoClassName: class com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 18:42:15.733 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- About to send an 'adminMessage' to this workflow (clients_connections_details) about this client connection: ProducerRecord(topic=devcloud_clients_connections_details, partition=null, headers=RecordHeaders(headers = [], isReadOnly = false), key=market_price_list_updated|DPDA_Market_Price_Consumer|CA-JT26LR3, value={"clientId": "DPDA_Market_Price_Consumer", "workflowId": "market_price_list_updated", "connectionDescriptor": {"region": "aws-us-east-1", "env": "devcloud", "hostname": "CA-JT26LR3", "port": -1, "pid": 36628, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757716935646", "endDateTime": null, "error": null}, timestamp=null)
2025-09-12 18:42:16.316 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Sent an 'adminMessage' to this workflow (clients_connections_details) about this client connection: ProducerRecord(topic=devcloud_clients_connections_details, partition=null, headers=RecordHeaders(headers = [], isReadOnly = true), key=market_price_list_updated|DPDA_Market_Price_Consumer|CA-JT26LR3, value={"clientId": "DPDA_Market_Price_Consumer", "workflowId": "market_price_list_updated", "connectionDescriptor": {"region": "aws-us-east-1", "env": "devcloud", "hostname": "CA-JT26LR3", "port": -1, "pid": 36628, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757716935646", "endDateTime": null, "error": null}, timestamp=null)
2025-09-12 18:42:16.694 [main] WARN  [] c.m.d.m.common.impl.KafkaFactory -- This method is being deprecated, we should not use it going forward! Please use other overloaded methods. Done building fully configured consumer (org.apache.kafka.clients.consumer.KafkaConsumer@524ba12b). 
Morningstar custom properties:
	{tasks_number_tag=1, serde_pojo_class_name_tag=com.morningstar.data.domain.eod.events.MarketPriceEvent, topic_name_tag=devcloud_market_price_list_updated}. 
This Consumer Connection Details:
	{"clientId": "DPDA_Market_Price_Consumer", "workflowId": "market_price_list_updated", "connectionDescriptor": {"region": "aws-us-east-1", "env": "devcloud", "hostname": "CA-JT26LR3", "port": -1, "pid": 36628, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757716935646", "endDateTime": null, "error": null}
2025-09-12 18:42:16.702 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- workflow market_price_list_updated enable.auto.commit is configured as false
2025-09-12 18:42:16.703 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- Done initialize this 'RunnableConsumer' - Details:
	Workflow: market_price_list_updated
	Overrides: [{enable.auto.commit=false, serde_pojo_class_name_tag=com.morningstar.data.domain.eod.events.MarketPriceEvent, group.id=dpda_market_price_group, auto.offset.reset=earliest, client.id=DPDA_Market_Price_Consumer}]
2025-09-12 18:42:16.704 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Done building fully configured consumer (com.morningstar.dp.messaging.common.impl.RunnableConsumer@5d7807b0)
2025-09-12 18:42:16.705 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- [DEPRECATED-Should not be here] - Done setting a system property for serde class (key:serde_pojo_class_name_tag, value:com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent)
2025-09-12 18:42:16.706 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent, codecName: null)
2025-09-12 18:42:16.709 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- This generic serde is using (serdePojoClassName: class com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent, codecName: null)
2025-09-12 18:42:16.709 [main] WARN  [] c.m.d.m.common.impl.KafkaFactory -- client id DPDA_Market_Price_Consumer already exists, will change it to DPDA_Market_Price_Consumer-0
2025-09-12 18:42:16.714 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 18:42:16.715 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- This generic serde is using (serdePojoClassName: class com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 18:42:16.715 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- About to send an 'adminMessage' to this workflow (clients_connections_details) about this client connection: ProducerRecord(topic=devcloud_clients_connections_details, partition=null, headers=RecordHeaders(headers = [], isReadOnly = false), key=market_price_enriched_event|DPDA_Market_Price_Consumer-0|CA-JT26LR3, value={"clientId": "DPDA_Market_Price_Consumer-0", "workflowId": "market_price_enriched_event", "connectionDescriptor": {"region": "aws-us-east-1", "env": "devcloud", "hostname": "CA-JT26LR3", "port": -1, "pid": 36628, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757716936714", "endDateTime": null, "error": null}, timestamp=null)
2025-09-12 18:42:16.715 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- This is for auditing only so no issue to worry about. Will not produce into the kafka-Plugin admin topic (clients_connections_details) for now.
2025-09-12 18:42:16.715 [main] WARN  [] c.m.d.m.common.impl.KafkaFactory -- This method is being deprecated, we should not use it going forward! Please use other overloaded methods. Done building fully configured consumer (org.apache.kafka.clients.consumer.KafkaConsumer@5d9ac1ff). 
Morningstar custom properties:
	{tasks_number_tag=1, serde_pojo_class_name_tag=com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent, topic_name_tag=devcloud_market_price_enriched_event}. 
This Consumer Connection Details:
	{"clientId": "DPDA_Market_Price_Consumer-0", "workflowId": "market_price_enriched_event", "connectionDescriptor": {"region": "aws-us-east-1", "env": "devcloud", "hostname": "CA-JT26LR3", "port": -1, "pid": 36628, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757716936714", "endDateTime": null, "error": null}
2025-09-12 18:42:16.715 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- workflow market_price_enriched_event enable.auto.commit is configured as false
2025-09-12 18:42:16.715 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- Done initialize this 'RunnableConsumer' - Details:
	Workflow: market_price_enriched_event
	Overrides: [{enable.auto.commit=false, serde_pojo_class_name_tag=com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent, group.id=dpda_market_price_group, auto.offset.reset=earliest, client.id=DPDA_Market_Price_Consumer}]
2025-09-12 18:42:16.715 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Done building fully configured consumer (com.morningstar.dp.messaging.common.impl.RunnableConsumer@6ff879e5)
2025-09-12 18:42:17.065 [main] INFO  [] c.m.m.MartKafkaConsumerApplication -- Started MartKafkaConsumerApplication in 4.913 seconds (process running for 5.417)
2025-09-12 18:42:17.072 [main] INFO  [] c.m.m.c.AbstractMessageConsumer -- Starting Kafka message consumer for topic: market_price_enriched_event
2025-09-12 18:42:17.073 [main] INFO  [] c.m.m.c.AbstractMessageConsumer -- Kafka message consumer started successfully
2025-09-12 18:42:44.919 [runnable-consumer-0] INFO  [f7048bdf-2762-400d-b059-22c3512448b0] c.m.m.c.KafkaPluginConsumerEndpoint -- topic: devcloud_market_price_enriched_event, record partition 0, offset: 35, key: null, pollId: f7048bdf-2762-400d-b059-22c3512448b0, value: {"market_price_data_list": [{"performance_id": "PERF_0", "investment_type": "Stock", "date": 2025-09-09, "high": 100.00000000000000000, "low": 90.00000000000000000, "open": 95.00000000000000000, "close": 98.00000000000000000, "bid": null, "mid": null, "ask": null, "currency_code": "USD", "traded_volume": 1000000.00000000000000000, "copy_over_reason": "PRICE"}], "exchange_detail": null, "batch_metadata": null, "event_timestamp_utc": 2025-09-12T22:42:44.124Z}
2025-09-12 18:42:44.919 [runnable-consumer-0] INFO  [f7048bdf-2762-400d-b059-22c3512448b0] c.m.m.c.AbstractMessageConsumer -- Received 1 messages with pollId: f7048bdf-2762-400d-b059-22c3512448b0
2025-09-12 18:42:44.919 [runnable-consumer-0] INFO  [f7048bdf-2762-400d-b059-22c3512448b0] c.m.m.c.MarketPriceEnrichedConsumer -- MarketPriceEnrichedConsumer Processing 1 messages with pollId: f7048bdf-2762-400d-b059-22c3512448b0
2025-09-12 18:42:44.921 [pool-3-thread-1] INFO  [f7048bdf-2762-400d-b059-22c3512448b0] c.m.m.service.MarketPriceDataService -- Storing market price data for investmentId: PERF_0, date: 2025-09-09
2025-09-12 18:42:46.901 [pool-3-thread-1] INFO  [f7048bdf-2762-400d-b059-22c3512448b0] c.m.m.service.MarketPriceDataService -- Batch stored 5 data points for key: ts:MarketPriceAdjusted:PERF_0:2020
2025-09-12 18:42:46.901 [runnable-consumer-0] INFO  [f7048bdf-2762-400d-b059-22c3512448b0] c.m.m.c.MarketPriceEnrichedConsumer -- MarketPriceEnrichedConsumer: Completed processing 1 messages with pollId: f7048bdf-2762-400d-b059-22c3512448b0 in 1982ms
2025-09-12 18:42:46.902 [runnable-consumer-0] INFO  [f7048bdf-2762-400d-b059-22c3512448b0] c.m.m.c.KafkaPluginConsumerEndpoint -- Committing topic: devcloud_market_price_enriched_event offsets: {0=36}
2025-09-12 18:42:46.908 [runnable-consumer-0] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- processAsyncCommit() is called with {devcloud_market_price_enriched_event-0=OffsetAndMetadata{offset=36, leaderEpoch=null, metadata=''}}
2025-09-12 18:42:46.993 [runnable-consumer-1] INFO  [] c.m.m.c.KafkaPluginConsumerEndpoint -- Committed topic: devcloud_market_price_enriched_event, partition: 0, offset: 36
2025-09-12 18:45:10.382 [runnable-consumer-0] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- Trying to close Kafka consumer, RunnableConsumer.isRunning: false
2025-09-12 18:50:56.616 [main] INFO  [] c.m.m.security.AuthInitializer -- [Authentication] Keytab: C:\Users\<USER>\AppData\Local\Temp\service-keytab-17496279813120100226.keytab
2025-09-12 18:50:56.617 [main] INFO  [] c.m.m.security.AuthInitializer -- [Authentication] JAAS conf: C:\Users\<USER>\AppData\Local\Temp\jaas-13452145912560911146.conf
2025-09-12 18:50:56.617 [main] INFO  [] c.m.m.security.AuthInitializer -- [Authentication] krb5 conf: C:\Users\<USER>\AppData\Local\Temp\krb5-6744426271797347714.conf
2025-09-12 18:50:56.620 [main] INFO  [] c.m.m.MartKafkaConsumerApplication -- Starting MartKafkaConsumerApplication using Java 17.0.9 with PID 34212 (C:\Zeng\dataac\msstash\mart-kafka-consumer\target\classes started by wzeng1 in C:\Zeng\dataac\msstash\mart-kafka-consumer)
2025-09-12 18:50:56.620 [main] DEBUG [] c.m.m.MartKafkaConsumerApplication -- Running with Spring Boot v3.5.5, Spring v6.2.10
2025-09-12 18:50:56.621 [main] INFO  [] c.m.m.MartKafkaConsumerApplication -- The following 1 profile is active: "dev"
2025-09-12 18:50:56.897 [main] INFO  [] o.s.d.r.c.RepositoryConfigurationDelegate -- Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-12 18:50:56.898 [main] INFO  [] o.s.d.r.c.RepositoryConfigurationDelegate -- Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-12 18:50:56.922 [main] INFO  [] o.s.d.r.c.RepositoryConfigurationDelegate -- Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
2025-09-12 18:50:57.049 [main] INFO  [] c.m.m.config.ExecutorConfig -- Created MarketPrice ExecutorService with 20 threads
2025-09-12 18:50:58.302 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Kafka factory fully initialized with region (aws-us-east-1) and env (devcloud)
2025-09-12 18:50:58.314 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- [DEPRECATED-Should not be here] - Done setting a system property for serde class (key:serde_pojo_class_name_tag, value:com.morningstar.data.domain.eod.events.MarketPriceEvent)
2025-09-12 18:50:58.316 [main] INFO  [] c.m.d.m.c.s.a.AvroNoSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.eod.events.MarketPriceEvent, codecName: null)
2025-09-12 18:50:58.546 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 18:50:58.548 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- This generic serde is using (serdePojoClassName: class com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 18:50:58.632 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- About to send an 'adminMessage' to this workflow (clients_connections_details) about this client connection: ProducerRecord(topic=devcloud_clients_connections_details, partition=null, headers=RecordHeaders(headers = [], isReadOnly = false), key=market_price_list_updated|DPDA_Market_Price_Consumer|CA-JT26LR3, value={"clientId": "DPDA_Market_Price_Consumer", "workflowId": "market_price_list_updated", "connectionDescriptor": {"region": "aws-us-east-1", "env": "devcloud", "hostname": "CA-JT26LR3", "port": -1, "pid": 34212, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757717458544", "endDateTime": null, "error": null}, timestamp=null)
2025-09-12 18:50:59.227 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Sent an 'adminMessage' to this workflow (clients_connections_details) about this client connection: ProducerRecord(topic=devcloud_clients_connections_details, partition=null, headers=RecordHeaders(headers = [], isReadOnly = true), key=market_price_list_updated|DPDA_Market_Price_Consumer|CA-JT26LR3, value={"clientId": "DPDA_Market_Price_Consumer", "workflowId": "market_price_list_updated", "connectionDescriptor": {"region": "aws-us-east-1", "env": "devcloud", "hostname": "CA-JT26LR3", "port": -1, "pid": 34212, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757717458544", "endDateTime": null, "error": null}, timestamp=null)
2025-09-12 18:50:59.620 [main] WARN  [] c.m.d.m.common.impl.KafkaFactory -- This method is being deprecated, we should not use it going forward! Please use other overloaded methods. Done building fully configured consumer (org.apache.kafka.clients.consumer.KafkaConsumer@8508dae). 
Morningstar custom properties:
	{tasks_number_tag=1, serde_pojo_class_name_tag=com.morningstar.data.domain.eod.events.MarketPriceEvent, topic_name_tag=devcloud_market_price_list_updated}. 
This Consumer Connection Details:
	{"clientId": "DPDA_Market_Price_Consumer", "workflowId": "market_price_list_updated", "connectionDescriptor": {"region": "aws-us-east-1", "env": "devcloud", "hostname": "CA-JT26LR3", "port": -1, "pid": 34212, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757717458544", "endDateTime": null, "error": null}
2025-09-12 18:50:59.640 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- workflow market_price_list_updated enable.auto.commit is configured as false
2025-09-12 18:50:59.640 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- Done initialize this 'RunnableConsumer' - Details:
	Workflow: market_price_list_updated
	Overrides: [{enable.auto.commit=false, serde_pojo_class_name_tag=com.morningstar.data.domain.eod.events.MarketPriceEvent, group.id=dpda_market_price_group, auto.offset.reset=earliest, client.id=DPDA_Market_Price_Consumer}]
2025-09-12 18:50:59.640 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Done building fully configured consumer (com.morningstar.dp.messaging.common.impl.RunnableConsumer@81cba04)
2025-09-12 18:50:59.642 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- [DEPRECATED-Should not be here] - Done setting a system property for serde class (key:serde_pojo_class_name_tag, value:com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent)
2025-09-12 18:50:59.643 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent, codecName: null)
2025-09-12 18:50:59.648 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- This generic serde is using (serdePojoClassName: class com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent, codecName: null)
2025-09-12 18:50:59.649 [main] WARN  [] c.m.d.m.common.impl.KafkaFactory -- client id DPDA_Market_Price_Consumer already exists, will change it to DPDA_Market_Price_Consumer-0
2025-09-12 18:50:59.657 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 18:50:59.657 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- This generic serde is using (serdePojoClassName: class com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 18:50:59.658 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- About to send an 'adminMessage' to this workflow (clients_connections_details) about this client connection: ProducerRecord(topic=devcloud_clients_connections_details, partition=null, headers=RecordHeaders(headers = [], isReadOnly = false), key=market_price_enriched_event|DPDA_Market_Price_Consumer-0|CA-JT26LR3, value={"clientId": "DPDA_Market_Price_Consumer-0", "workflowId": "market_price_enriched_event", "connectionDescriptor": {"region": "aws-us-east-1", "env": "devcloud", "hostname": "CA-JT26LR3", "port": -1, "pid": 34212, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757717459657", "endDateTime": null, "error": null}, timestamp=null)
2025-09-12 18:50:59.659 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- This is for auditing only so no issue to worry about. Will not produce into the kafka-Plugin admin topic (clients_connections_details) for now.
2025-09-12 18:50:59.659 [main] WARN  [] c.m.d.m.common.impl.KafkaFactory -- This method is being deprecated, we should not use it going forward! Please use other overloaded methods. Done building fully configured consumer (org.apache.kafka.clients.consumer.KafkaConsumer@7080e8fc). 
Morningstar custom properties:
	{tasks_number_tag=1, serde_pojo_class_name_tag=com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent, topic_name_tag=devcloud_market_price_enriched_event}. 
This Consumer Connection Details:
	{"clientId": "DPDA_Market_Price_Consumer-0", "workflowId": "market_price_enriched_event", "connectionDescriptor": {"region": "aws-us-east-1", "env": "devcloud", "hostname": "CA-JT26LR3", "port": -1, "pid": 34212, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757717459657", "endDateTime": null, "error": null}
2025-09-12 18:50:59.659 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- workflow market_price_enriched_event enable.auto.commit is configured as false
2025-09-12 18:50:59.660 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- Done initialize this 'RunnableConsumer' - Details:
	Workflow: market_price_enriched_event
	Overrides: [{enable.auto.commit=false, serde_pojo_class_name_tag=com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent, group.id=dpda_market_price_group, auto.offset.reset=earliest, client.id=DPDA_Market_Price_Consumer}]
2025-09-12 18:50:59.660 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Done building fully configured consumer (com.morningstar.dp.messaging.common.impl.RunnableConsumer@25b8a2f7)
2025-09-12 18:51:00.029 [main] INFO  [] c.m.m.MartKafkaConsumerApplication -- Started MartKafkaConsumerApplication in 4.844 seconds (process running for 5.338)
2025-09-12 18:51:00.037 [main] INFO  [] c.m.m.c.AbstractMessageConsumer -- Starting Kafka message consumer for topic: market_price_enriched_event
2025-09-12 18:51:00.038 [main] INFO  [] c.m.m.c.AbstractMessageConsumer -- Kafka message consumer started successfully
2025-09-12 18:51:04.325 [runnable-consumer-0] INFO  [ba89fc57-470e-452f-b8f0-2a6b518e1cf2] c.m.m.c.KafkaPluginConsumerEndpoint -- topic: devcloud_market_price_enriched_event, record partition 0, offset: 36, key: null, pollId: ba89fc57-470e-452f-b8f0-2a6b518e1cf2, value: {"market_price_data_list": [{"performance_id": "PERF_0", "investment_type": "Stock", "date": 2025-09-09, "high": 100.00000000000000000, "low": 90.00000000000000000, "open": 95.00000000000000000, "close": 98.00000000000000000, "bid": null, "mid": null, "ask": null, "currency_code": "USD", "traded_volume": 1000000.00000000000000000, "copy_over_reason": "PRICE"}], "exchange_detail": null, "batch_metadata": null, "event_timestamp_utc": 2025-09-12T22:45:25.578Z}
2025-09-12 18:51:04.326 [runnable-consumer-0] INFO  [ba89fc57-470e-452f-b8f0-2a6b518e1cf2] c.m.m.c.KafkaPluginConsumerEndpoint -- topic: devcloud_market_price_enriched_event, record partition 0, offset: 37, key: null, pollId: ba89fc57-470e-452f-b8f0-2a6b518e1cf2, value: {"market_price_data_list": [{"performance_id": "PERF_0", "investment_type": "Stock", "date": 2025-09-09, "high": 100.00000000000000000, "low": 90.00000000000000000, "open": 95.00000000000000000, "close": 98.00000000000000000, "bid": null, "mid": null, "ask": null, "currency_code": "USD", "traded_volume": 1000000.00000000000000000, "copy_over_reason": "PRICE"}], "exchange_detail": null, "batch_metadata": null, "event_timestamp_utc": 2025-09-12T22:45:34.034Z}
2025-09-12 18:51:04.326 [runnable-consumer-0] INFO  [ba89fc57-470e-452f-b8f0-2a6b518e1cf2] c.m.m.c.KafkaPluginConsumerEndpoint -- topic: devcloud_market_price_enriched_event, record partition 0, offset: 38, key: null, pollId: ba89fc57-470e-452f-b8f0-2a6b518e1cf2, value: {"market_price_data_list": [{"performance_id": "PERF_0", "investment_type": "Stock", "date": 2025-09-09, "high": 100.00000000000000000, "low": 90.00000000000000000, "open": 95.00000000000000000, "close": 98.00000000000000000, "bid": null, "mid": null, "ask": null, "currency_code": "USD", "traded_volume": 1000000.00000000000000000, "copy_over_reason": "PRICE"}], "exchange_detail": null, "batch_metadata": null, "event_timestamp_utc": 2025-09-12T22:50:06.442Z}
2025-09-12 18:51:04.326 [runnable-consumer-0] INFO  [ba89fc57-470e-452f-b8f0-2a6b518e1cf2] c.m.m.c.AbstractMessageConsumer -- Received 3 messages with pollId: ba89fc57-470e-452f-b8f0-2a6b518e1cf2
2025-09-12 18:51:04.326 [runnable-consumer-0] INFO  [ba89fc57-470e-452f-b8f0-2a6b518e1cf2] c.m.m.c.MarketPriceEnrichedConsumer -- MarketPriceEnrichedConsumer Processing 3 messages with pollId: ba89fc57-470e-452f-b8f0-2a6b518e1cf2
2025-09-12 18:51:04.329 [runnable-consumer-0] ERROR [ba89fc57-470e-452f-b8f0-2a6b518e1cf2] c.m.m.c.AbstractMessageConsumer -- Default error handling: failed to process 3 messages, pollId: ba89fc57-470e-452f-b8f0-2a6b518e1cf2
java.util.concurrent.CompletionException: java.lang.RuntimeException: MarketPriceEnrichedConsumer: Failed to process message
	at java.base/java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:315)
	at java.base/java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:320)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1807)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.lang.RuntimeException: MarketPriceEnrichedConsumer: Failed to process message
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.processMessage(MarketPriceEnrichedConsumer.java:55)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$0(MarketPriceEnrichedConsumer.java:39)
	at com.morningstar.martkafkaconsumer.util.LoggingContext.withTraceId(LoggingContext.java:31)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$1(MarketPriceEnrichedConsumer.java:39)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	... 3 common frames omitted
2025-09-12 18:51:04.332 [runnable-consumer-0] INFO  [ba89fc57-470e-452f-b8f0-2a6b518e1cf2] c.m.m.c.KafkaPluginConsumerEndpoint -- Committing topic: devcloud_market_price_enriched_event offsets: {0=39}
2025-09-12 18:51:04.335 [runnable-consumer-0] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- processAsyncCommit() is called with {devcloud_market_price_enriched_event-0=OffsetAndMetadata{offset=39, leaderEpoch=null, metadata=''}}
2025-09-12 18:51:04.434 [runnable-consumer-1] INFO  [] c.m.m.c.KafkaPluginConsumerEndpoint -- Committed topic: devcloud_market_price_enriched_event, partition: 0, offset: 39
2025-09-12 18:54:30.202 [runnable-consumer-0] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- Trying to close Kafka consumer, RunnableConsumer.isRunning: false
2025-09-12 18:54:34.979 [main] INFO  [] c.m.m.security.AuthInitializer -- [Authentication] Keytab: C:\Users\<USER>\AppData\Local\Temp\service-keytab-15329817834460326394.keytab
2025-09-12 18:54:34.981 [main] INFO  [] c.m.m.security.AuthInitializer -- [Authentication] JAAS conf: C:\Users\<USER>\AppData\Local\Temp\jaas-3441456749609711095.conf
2025-09-12 18:54:34.981 [main] INFO  [] c.m.m.security.AuthInitializer -- [Authentication] krb5 conf: C:\Users\<USER>\AppData\Local\Temp\krb5-4570181782110359824.conf
2025-09-12 18:54:34.986 [main] INFO  [] c.m.m.MartKafkaConsumerApplication -- Starting MartKafkaConsumerApplication using Java 17.0.9 with PID 35464 (C:\Zeng\dataac\msstash\mart-kafka-consumer\target\classes started by wzeng1 in C:\Zeng\dataac\msstash\mart-kafka-consumer)
2025-09-12 18:54:34.986 [main] DEBUG [] c.m.m.MartKafkaConsumerApplication -- Running with Spring Boot v3.5.5, Spring v6.2.10
2025-09-12 18:54:34.988 [main] INFO  [] c.m.m.MartKafkaConsumerApplication -- The following 1 profile is active: "dev"
2025-09-12 18:54:35.292 [main] INFO  [] o.s.d.r.c.RepositoryConfigurationDelegate -- Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-12 18:54:35.294 [main] INFO  [] o.s.d.r.c.RepositoryConfigurationDelegate -- Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-12 18:54:35.322 [main] INFO  [] o.s.d.r.c.RepositoryConfigurationDelegate -- Finished Spring Data repository scanning in 14 ms. Found 0 Redis repository interfaces.
2025-09-12 18:54:35.467 [main] INFO  [] c.m.m.config.ExecutorConfig -- Created MarketPrice ExecutorService with 20 threads
2025-09-12 18:54:36.841 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Kafka factory fully initialized with region (aws-us-east-1) and env (devcloud)
2025-09-12 18:54:36.864 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- [DEPRECATED-Should not be here] - Done setting a system property for serde class (key:serde_pojo_class_name_tag, value:com.morningstar.data.domain.eod.events.MarketPriceEvent)
2025-09-12 18:54:36.867 [main] INFO  [] c.m.d.m.c.s.a.AvroNoSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.eod.events.MarketPriceEvent, codecName: null)
2025-09-12 18:54:37.117 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 18:54:37.119 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- This generic serde is using (serdePojoClassName: class com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 18:54:37.206 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- About to send an 'adminMessage' to this workflow (clients_connections_details) about this client connection: ProducerRecord(topic=devcloud_clients_connections_details, partition=null, headers=RecordHeaders(headers = [], isReadOnly = false), key=market_price_list_updated|DPDA_Market_Price_Consumer|CA-JT26LR3, value={"clientId": "DPDA_Market_Price_Consumer", "workflowId": "market_price_list_updated", "connectionDescriptor": {"region": "aws-us-east-1", "env": "devcloud", "hostname": "CA-JT26LR3", "port": -1, "pid": 35464, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757717677116", "endDateTime": null, "error": null}, timestamp=null)
2025-09-12 18:54:37.845 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Sent an 'adminMessage' to this workflow (clients_connections_details) about this client connection: ProducerRecord(topic=devcloud_clients_connections_details, partition=null, headers=RecordHeaders(headers = [], isReadOnly = true), key=market_price_list_updated|DPDA_Market_Price_Consumer|CA-JT26LR3, value={"clientId": "DPDA_Market_Price_Consumer", "workflowId": "market_price_list_updated", "connectionDescriptor": {"region": "aws-us-east-1", "env": "devcloud", "hostname": "CA-JT26LR3", "port": -1, "pid": 35464, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757717677116", "endDateTime": null, "error": null}, timestamp=null)
2025-09-12 18:54:38.208 [main] WARN  [] c.m.d.m.common.impl.KafkaFactory -- This method is being deprecated, we should not use it going forward! Please use other overloaded methods. Done building fully configured consumer (org.apache.kafka.clients.consumer.KafkaConsumer@50ca509b). 
Morningstar custom properties:
	{tasks_number_tag=1, serde_pojo_class_name_tag=com.morningstar.data.domain.eod.events.MarketPriceEvent, topic_name_tag=devcloud_market_price_list_updated}. 
This Consumer Connection Details:
	{"clientId": "DPDA_Market_Price_Consumer", "workflowId": "market_price_list_updated", "connectionDescriptor": {"region": "aws-us-east-1", "env": "devcloud", "hostname": "CA-JT26LR3", "port": -1, "pid": 35464, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757717677116", "endDateTime": null, "error": null}
2025-09-12 18:54:38.219 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- workflow market_price_list_updated enable.auto.commit is configured as false
2025-09-12 18:54:38.220 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- Done initialize this 'RunnableConsumer' - Details:
	Workflow: market_price_list_updated
	Overrides: [{enable.auto.commit=false, serde_pojo_class_name_tag=com.morningstar.data.domain.eod.events.MarketPriceEvent, group.id=dpda_market_price_group, auto.offset.reset=earliest, client.id=DPDA_Market_Price_Consumer}]
2025-09-12 18:54:38.220 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Done building fully configured consumer (com.morningstar.dp.messaging.common.impl.RunnableConsumer@280fa441)
2025-09-12 18:54:38.222 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- [DEPRECATED-Should not be here] - Done setting a system property for serde class (key:serde_pojo_class_name_tag, value:com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent)
2025-09-12 18:54:38.223 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent, codecName: null)
2025-09-12 18:54:38.225 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- This generic serde is using (serdePojoClassName: class com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent, codecName: null)
2025-09-12 18:54:38.225 [main] WARN  [] c.m.d.m.common.impl.KafkaFactory -- client id DPDA_Market_Price_Consumer already exists, will change it to DPDA_Market_Price_Consumer-0
2025-09-12 18:54:38.232 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 18:54:38.232 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- This generic serde is using (serdePojoClassName: class com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 18:54:38.232 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- About to send an 'adminMessage' to this workflow (clients_connections_details) about this client connection: ProducerRecord(topic=devcloud_clients_connections_details, partition=null, headers=RecordHeaders(headers = [], isReadOnly = false), key=market_price_enriched_event|DPDA_Market_Price_Consumer-0|CA-JT26LR3, value={"clientId": "DPDA_Market_Price_Consumer-0", "workflowId": "market_price_enriched_event", "connectionDescriptor": {"region": "aws-us-east-1", "env": "devcloud", "hostname": "CA-JT26LR3", "port": -1, "pid": 35464, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757717678231", "endDateTime": null, "error": null}, timestamp=null)
2025-09-12 18:54:38.232 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- This is for auditing only so no issue to worry about. Will not produce into the kafka-Plugin admin topic (clients_connections_details) for now.
2025-09-12 18:54:38.232 [main] WARN  [] c.m.d.m.common.impl.KafkaFactory -- This method is being deprecated, we should not use it going forward! Please use other overloaded methods. Done building fully configured consumer (org.apache.kafka.clients.consumer.KafkaConsumer@294c1b45). 
Morningstar custom properties:
	{tasks_number_tag=1, serde_pojo_class_name_tag=com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent, topic_name_tag=devcloud_market_price_enriched_event}. 
This Consumer Connection Details:
	{"clientId": "DPDA_Market_Price_Consumer-0", "workflowId": "market_price_enriched_event", "connectionDescriptor": {"region": "aws-us-east-1", "env": "devcloud", "hostname": "CA-JT26LR3", "port": -1, "pid": 35464, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757717678231", "endDateTime": null, "error": null}
2025-09-12 18:54:38.232 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- workflow market_price_enriched_event enable.auto.commit is configured as false
2025-09-12 18:54:38.232 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- Done initialize this 'RunnableConsumer' - Details:
	Workflow: market_price_enriched_event
	Overrides: [{enable.auto.commit=false, serde_pojo_class_name_tag=com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent, group.id=dpda_market_price_group, auto.offset.reset=earliest, client.id=DPDA_Market_Price_Consumer}]
2025-09-12 18:54:38.233 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Done building fully configured consumer (com.morningstar.dp.messaging.common.impl.RunnableConsumer@45070db0)
2025-09-12 18:54:38.653 [main] INFO  [] c.m.m.MartKafkaConsumerApplication -- Started MartKafkaConsumerApplication in 5.324 seconds (process running for 6.099)
2025-09-12 18:54:38.655 [main] INFO  [] c.m.m.c.AbstractMessageConsumer -- Starting Kafka message consumer for topic: market_price_enriched_event
2025-09-12 18:54:38.656 [main] INFO  [] c.m.m.c.AbstractMessageConsumer -- Kafka message consumer started successfully
2025-09-12 18:54:54.236 [runnable-consumer-0] INFO  [c5772ada-113e-47e2-8897-f11e1596ec53] c.m.m.c.KafkaPluginConsumerEndpoint -- topic: devcloud_market_price_enriched_event, record partition 0, offset: 39, key: null, pollId: c5772ada-113e-47e2-8897-f11e1596ec53, value: {"market_price_data_list": [{"performance_id": "PERF_0", "investment_type": "Stock", "date": 2025-09-09, "high": 100.00000000000000000, "low": 90.00000000000000000, "open": 95.00000000000000000, "close": 98.00000000000000000, "bid": null, "mid": null, "ask": null, "currency_code": "USD", "traded_volume": 1000000.00000000000000000, "copy_over_reason": "PRICE"}], "exchange_detail": null, "batch_metadata": null, "event_timestamp_utc": 2025-09-12T22:54:53.494Z}
2025-09-12 18:54:58.449 [runnable-consumer-0] INFO  [c5772ada-113e-47e2-8897-f11e1596ec53] c.m.m.c.AbstractMessageConsumer -- Received 1 messages with pollId: c5772ada-113e-47e2-8897-f11e1596ec53
2025-09-12 18:54:58.449 [runnable-consumer-0] INFO  [c5772ada-113e-47e2-8897-f11e1596ec53] c.m.m.c.MarketPriceEnrichedConsumer -- MarketPriceEnrichedConsumer Processing 1 messages with pollId: c5772ada-113e-47e2-8897-f11e1596ec53
2025-09-12 18:54:58.454 [runnable-consumer-0] ERROR [c5772ada-113e-47e2-8897-f11e1596ec53] c.m.m.c.AbstractMessageConsumer -- Default error handling: failed to process 1 messages, pollId: c5772ada-113e-47e2-8897-f11e1596ec53
java.util.concurrent.CompletionException: java.lang.RuntimeException: MarketPriceEnrichedConsumer: Failed to process message
	at java.base/java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:315)
	at java.base/java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:320)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1807)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.lang.RuntimeException: MarketPriceEnrichedConsumer: Failed to process message
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.processMessage(MarketPriceEnrichedConsumer.java:55)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$0(MarketPriceEnrichedConsumer.java:39)
	at com.morningstar.martkafkaconsumer.util.LoggingContext.withTraceId(LoggingContext.java:31)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$1(MarketPriceEnrichedConsumer.java:39)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1804)
	... 4 common frames omitted
2025-09-12 18:55:43.640 [runnable-consumer-0] INFO  [c5772ada-113e-47e2-8897-f11e1596ec53] c.m.m.c.KafkaPluginConsumerEndpoint -- Committing topic: devcloud_market_price_enriched_event offsets: {0=40}
2025-09-12 18:55:43.649 [runnable-consumer-0] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- processAsyncCommit() is called with {devcloud_market_price_enriched_event-0=OffsetAndMetadata{offset=40, leaderEpoch=null, metadata=''}}
2025-09-12 18:55:44.075 [runnable-consumer-0] ERROR [] o.a.k.c.c.i.ConsumerCoordinator -- [Consumer clientId=DPDA_Market_Price_Consumer-0, groupId=dpda_market_price_group] Offset commit failed on partition devcloud_market_price_enriched_event-0 at offset 40: The coordinator is not aware of this member.
2025-09-12 18:55:44.078 [runnable-consumer-1] ERROR [] c.m.m.c.KafkaPluginConsumerEndpoint -- Failed to commit offsets: {devcloud_market_price_enriched_event-0=OffsetAndMetadata{offset=40, leaderEpoch=null, metadata=''}}
org.apache.kafka.clients.consumer.CommitFailedException: Commit cannot be completed since the group has already rebalanced and assigned the partitions to another member. This means that the time between subsequent calls to poll() was longer than the configured max.poll.interval.ms, which typically implies that the poll loop is spending too much time message processing. You can address this either by increasing max.poll.interval.ms or by reducing the maximum size of batches returned in poll() with max.poll.records.
	at org.apache.kafka.clients.consumer.internals.ConsumerCoordinator$OffsetCommitResponseHandler.handle(ConsumerCoordinator.java:1405)
	at org.apache.kafka.clients.consumer.internals.ConsumerCoordinator$OffsetCommitResponseHandler.handle(ConsumerCoordinator.java:1305)
	at org.apache.kafka.clients.consumer.internals.AbstractCoordinator$CoordinatorResponseHandler.onSuccess(AbstractCoordinator.java:1311)
	at org.apache.kafka.clients.consumer.internals.AbstractCoordinator$CoordinatorResponseHandler.onSuccess(AbstractCoordinator.java:1286)
	at org.apache.kafka.clients.consumer.internals.RequestFuture$1.onSuccess(RequestFuture.java:206)
	at org.apache.kafka.clients.consumer.internals.RequestFuture.fireSuccess(RequestFuture.java:169)
	at org.apache.kafka.clients.consumer.internals.RequestFuture.complete(RequestFuture.java:129)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient$RequestFutureCompletionHandler.fireCompletion(ConsumerNetworkClient.java:617)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.firePendingCompletedRequests(ConsumerNetworkClient.java:429)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:314)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:253)
	at org.apache.kafka.clients.consumer.internals.ClassicKafkaConsumer.pollForFetches(ClassicKafkaConsumer.java:692)
	at org.apache.kafka.clients.consumer.internals.ClassicKafkaConsumer.poll(ClassicKafkaConsumer.java:623)
	at org.apache.kafka.clients.consumer.internals.ClassicKafkaConsumer.poll(ClassicKafkaConsumer.java:591)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:827)
	at com.morningstar.dp.messaging.common.impl.RunnableConsumer$ConsumerTask.notifyListener(RunnableConsumer.java:426)
	at com.morningstar.dp.messaging.common.impl.RunnableConsumer$ConsumerTask.run(RunnableConsumer.java:399)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-09-12 18:55:47.424 [runnable-consumer-0] INFO  [7dc75f1c-c565-4177-b323-ab409d1bf2ae] c.m.m.c.KafkaPluginConsumerEndpoint -- topic: devcloud_market_price_enriched_event, record partition 0, offset: 39, key: null, pollId: 7dc75f1c-c565-4177-b323-ab409d1bf2ae, value: {"market_price_data_list": [{"performance_id": "PERF_0", "investment_type": "Stock", "date": 2025-09-09, "high": 100.00000000000000000, "low": 90.00000000000000000, "open": 95.00000000000000000, "close": 98.00000000000000000, "bid": null, "mid": null, "ask": null, "currency_code": "USD", "traded_volume": 1000000.00000000000000000, "copy_over_reason": "PRICE"}], "exchange_detail": null, "batch_metadata": null, "event_timestamp_utc": 2025-09-12T22:54:53.494Z}
2025-09-12 18:58:28.263 [runnable-consumer-0] INFO  [7dc75f1c-c565-4177-b323-ab409d1bf2ae] c.m.m.c.AbstractMessageConsumer -- Received 1 messages with pollId: 7dc75f1c-c565-4177-b323-ab409d1bf2ae
2025-09-12 18:58:28.265 [runnable-consumer-0] INFO  [7dc75f1c-c565-4177-b323-ab409d1bf2ae] c.m.m.c.MarketPriceEnrichedConsumer -- MarketPriceEnrichedConsumer Processing 1 messages with pollId: 7dc75f1c-c565-4177-b323-ab409d1bf2ae
2025-09-12 18:58:28.267 [runnable-consumer-0] ERROR [7dc75f1c-c565-4177-b323-ab409d1bf2ae] c.m.m.c.AbstractMessageConsumer -- Default error handling: failed to process 1 messages, pollId: 7dc75f1c-c565-4177-b323-ab409d1bf2ae
java.util.concurrent.CompletionException: java.lang.RuntimeException: MarketPriceEnrichedConsumer: Failed to process message
	at java.base/java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:315)
	at java.base/java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:320)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1807)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.lang.RuntimeException: MarketPriceEnrichedConsumer: Failed to process message
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.processMessage(MarketPriceEnrichedConsumer.java:55)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$0(MarketPriceEnrichedConsumer.java:39)
	at com.morningstar.martkafkaconsumer.util.LoggingContext.withTraceId(LoggingContext.java:31)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$1(MarketPriceEnrichedConsumer.java:39)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1804)
	... 4 common frames omitted
2025-09-12 18:58:28.269 [runnable-consumer-0] INFO  [7dc75f1c-c565-4177-b323-ab409d1bf2ae] c.m.m.c.KafkaPluginConsumerEndpoint -- Committing topic: devcloud_market_price_enriched_event offsets: {0=40}
2025-09-12 18:58:28.270 [runnable-consumer-0] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- processAsyncCommit() is called with {devcloud_market_price_enriched_event-0=OffsetAndMetadata{offset=40, leaderEpoch=null, metadata=''}}
2025-09-12 18:58:28.455 [runnable-consumer-0] INFO  [d99a23d0-52bd-4911-b287-8be5d2b87f57] c.m.m.c.KafkaPluginConsumerEndpoint -- topic: devcloud_market_price_enriched_event, record partition 0, offset: 40, key: null, pollId: d99a23d0-52bd-4911-b287-8be5d2b87f57, value: {"market_price_data_list": [{"performance_id": "PERF_0", "investment_type": "Stock", "date": 2025-09-09, "high": 100.00000000000000000, "low": 90.00000000000000000, "open": 95.00000000000000000, "close": 98.00000000000000000, "bid": null, "mid": null, "ask": null, "currency_code": "USD", "traded_volume": 1000000.00000000000000000, "copy_over_reason": "PRICE"}], "exchange_detail": null, "batch_metadata": null, "event_timestamp_utc": 2025-09-12T22:57:45.480Z}
2025-09-12 18:58:28.455 [runnable-consumer-0] INFO  [d99a23d0-52bd-4911-b287-8be5d2b87f57] c.m.m.c.KafkaPluginConsumerEndpoint -- topic: devcloud_market_price_enriched_event, record partition 0, offset: 41, key: null, pollId: d99a23d0-52bd-4911-b287-8be5d2b87f57, value: {"market_price_data_list": [{"performance_id": "PERF_0", "investment_type": "Stock", "date": 2025-09-09, "high": 100.00000000000000000, "low": 90.00000000000000000, "open": 95.00000000000000000, "close": 98.00000000000000000, "bid": null, "mid": null, "ask": null, "currency_code": "USD", "traded_volume": 1000000.00000000000000000, "copy_over_reason": "PRICE"}], "exchange_detail": null, "batch_metadata": null, "event_timestamp_utc": 2025-09-12T22:58:22.942Z}
2025-09-12 18:58:28.455 [runnable-consumer-0] INFO  [d99a23d0-52bd-4911-b287-8be5d2b87f57] c.m.m.c.AbstractMessageConsumer -- Received 2 messages with pollId: d99a23d0-52bd-4911-b287-8be5d2b87f57
2025-09-12 18:58:28.455 [runnable-consumer-0] INFO  [d99a23d0-52bd-4911-b287-8be5d2b87f57] c.m.m.c.MarketPriceEnrichedConsumer -- MarketPriceEnrichedConsumer Processing 2 messages with pollId: d99a23d0-52bd-4911-b287-8be5d2b87f57
2025-09-12 18:58:28.458 [runnable-consumer-0] ERROR [d99a23d0-52bd-4911-b287-8be5d2b87f57] c.m.m.c.AbstractMessageConsumer -- Default error handling: failed to process 2 messages, pollId: d99a23d0-52bd-4911-b287-8be5d2b87f57
java.util.concurrent.CompletionException: java.lang.RuntimeException: MarketPriceEnrichedConsumer: Failed to process message
	at java.base/java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:315)
	at java.base/java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:320)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1807)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.lang.RuntimeException: MarketPriceEnrichedConsumer: Failed to process message
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.processMessage(MarketPriceEnrichedConsumer.java:55)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$0(MarketPriceEnrichedConsumer.java:39)
	at com.morningstar.martkafkaconsumer.util.LoggingContext.withTraceId(LoggingContext.java:31)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$1(MarketPriceEnrichedConsumer.java:39)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1804)
	... 4 common frames omitted
2025-09-12 18:58:28.458 [runnable-consumer-0] ERROR [] c.m.d.m.common.impl.RunnableConsumer -- Got an exception while trying to notify a listener with new payload(s)
java.util.concurrent.CompletionException: java.lang.RuntimeException: MarketPriceEnrichedConsumer: Failed to process message
	at java.base/java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:315)
	at java.base/java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:320)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1807)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.lang.RuntimeException: MarketPriceEnrichedConsumer: Failed to process message
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.processMessage(MarketPriceEnrichedConsumer.java:55)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$0(MarketPriceEnrichedConsumer.java:39)
	at com.morningstar.martkafkaconsumer.util.LoggingContext.withTraceId(LoggingContext.java:31)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$1(MarketPriceEnrichedConsumer.java:39)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1804)
	... 4 common frames omitted
2025-09-12 18:58:28.458 [runnable-consumer-0] ERROR [] c.m.m.c.KafkaPluginConsumerEndpoint -- Received IllegalStateException while consuming from kafka
java.util.concurrent.CompletionException: java.lang.RuntimeException: MarketPriceEnrichedConsumer: Failed to process message
	at java.base/java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:315)
	at java.base/java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:320)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1807)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.lang.RuntimeException: MarketPriceEnrichedConsumer: Failed to process message
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.processMessage(MarketPriceEnrichedConsumer.java:55)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$0(MarketPriceEnrichedConsumer.java:39)
	at com.morningstar.martkafkaconsumer.util.LoggingContext.withTraceId(LoggingContext.java:31)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$1(MarketPriceEnrichedConsumer.java:39)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1804)
	... 4 common frames omitted
2025-09-12 18:58:28.711 [runnable-consumer-0] ERROR [] o.a.k.c.c.i.ConsumerCoordinator -- [Consumer clientId=DPDA_Market_Price_Consumer-0, groupId=dpda_market_price_group] Offset commit failed on partition devcloud_market_price_enriched_event-0 at offset 40: The coordinator is not aware of this member.
2025-09-12 18:58:28.711 [runnable-consumer-1] ERROR [] c.m.m.c.KafkaPluginConsumerEndpoint -- Failed to commit offsets: {devcloud_market_price_enriched_event-0=OffsetAndMetadata{offset=40, leaderEpoch=null, metadata=''}}
org.apache.kafka.clients.consumer.CommitFailedException: Commit cannot be completed since the group has already rebalanced and assigned the partitions to another member. This means that the time between subsequent calls to poll() was longer than the configured max.poll.interval.ms, which typically implies that the poll loop is spending too much time message processing. You can address this either by increasing max.poll.interval.ms or by reducing the maximum size of batches returned in poll() with max.poll.records.
	at org.apache.kafka.clients.consumer.internals.ConsumerCoordinator$OffsetCommitResponseHandler.handle(ConsumerCoordinator.java:1405)
	at org.apache.kafka.clients.consumer.internals.ConsumerCoordinator$OffsetCommitResponseHandler.handle(ConsumerCoordinator.java:1305)
	at org.apache.kafka.clients.consumer.internals.AbstractCoordinator$CoordinatorResponseHandler.onSuccess(AbstractCoordinator.java:1311)
	at org.apache.kafka.clients.consumer.internals.AbstractCoordinator$CoordinatorResponseHandler.onSuccess(AbstractCoordinator.java:1286)
	at org.apache.kafka.clients.consumer.internals.RequestFuture$1.onSuccess(RequestFuture.java:206)
	at org.apache.kafka.clients.consumer.internals.RequestFuture.fireSuccess(RequestFuture.java:169)
	at org.apache.kafka.clients.consumer.internals.RequestFuture.complete(RequestFuture.java:129)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient$RequestFutureCompletionHandler.fireCompletion(ConsumerNetworkClient.java:617)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.firePendingCompletedRequests(ConsumerNetworkClient.java:429)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:314)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:253)
	at org.apache.kafka.clients.consumer.internals.ClassicKafkaConsumer.pollForFetches(ClassicKafkaConsumer.java:692)
	at org.apache.kafka.clients.consumer.internals.ClassicKafkaConsumer.poll(ClassicKafkaConsumer.java:623)
	at org.apache.kafka.clients.consumer.internals.ClassicKafkaConsumer.poll(ClassicKafkaConsumer.java:591)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:827)
	at com.morningstar.dp.messaging.common.impl.RunnableConsumer$ConsumerTask.notifyListener(RunnableConsumer.java:426)
	at com.morningstar.dp.messaging.common.impl.RunnableConsumer$ConsumerTask.run(RunnableConsumer.java:399)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-09-12 18:58:28.843 [runnable-consumer-0] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- Trying to close Kafka consumer, RunnableConsumer.isRunning: false
2025-09-12 18:58:36.047 [main] INFO  [] c.m.m.security.AuthInitializer -- [Authentication] Keytab: C:\Users\<USER>\AppData\Local\Temp\service-keytab-18158530703772477174.keytab
2025-09-12 18:58:36.049 [main] INFO  [] c.m.m.security.AuthInitializer -- [Authentication] JAAS conf: C:\Users\<USER>\AppData\Local\Temp\jaas-10191645679486197874.conf
2025-09-12 18:58:36.049 [main] INFO  [] c.m.m.security.AuthInitializer -- [Authentication] krb5 conf: C:\Users\<USER>\AppData\Local\Temp\krb5-10897253516495027079.conf
2025-09-12 18:58:36.052 [main] INFO  [] c.m.m.MartKafkaConsumerApplication -- Starting MartKafkaConsumerApplication using Java 17.0.9 with PID 2720 (C:\Zeng\dataac\msstash\mart-kafka-consumer\target\classes started by wzeng1 in C:\Zeng\dataac\msstash\mart-kafka-consumer)
2025-09-12 18:58:36.052 [main] DEBUG [] c.m.m.MartKafkaConsumerApplication -- Running with Spring Boot v3.5.5, Spring v6.2.10
2025-09-12 18:58:36.053 [main] INFO  [] c.m.m.MartKafkaConsumerApplication -- The following 1 profile is active: "dev"
2025-09-12 18:58:36.343 [main] INFO  [] o.s.d.r.c.RepositoryConfigurationDelegate -- Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-12 18:58:36.345 [main] INFO  [] o.s.d.r.c.RepositoryConfigurationDelegate -- Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-12 18:58:36.370 [main] INFO  [] o.s.d.r.c.RepositoryConfigurationDelegate -- Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces.
2025-09-12 18:58:36.512 [main] INFO  [] c.m.m.config.ExecutorConfig -- Created MarketPrice ExecutorService with 20 threads
2025-09-12 18:58:37.844 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Kafka factory fully initialized with region (aws-us-east-1) and env (devcloud)
2025-09-12 18:58:37.862 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- [DEPRECATED-Should not be here] - Done setting a system property for serde class (key:serde_pojo_class_name_tag, value:com.morningstar.data.domain.eod.events.MarketPriceEvent)
2025-09-12 18:58:37.864 [main] INFO  [] c.m.d.m.c.s.a.AvroNoSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.eod.events.MarketPriceEvent, codecName: null)
2025-09-12 18:58:38.126 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 18:58:38.128 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- This generic serde is using (serdePojoClassName: class com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 18:58:38.218 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- About to send an 'adminMessage' to this workflow (clients_connections_details) about this client connection: ProducerRecord(topic=devcloud_clients_connections_details, partition=null, headers=RecordHeaders(headers = [], isReadOnly = false), key=market_price_list_updated|DPDA_Market_Price_Consumer|CA-JT26LR3, value={"clientId": "DPDA_Market_Price_Consumer", "workflowId": "market_price_list_updated", "connectionDescriptor": {"region": "aws-us-east-1", "env": "devcloud", "hostname": "CA-JT26LR3", "port": -1, "pid": 2720, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757717918124", "endDateTime": null, "error": null}, timestamp=null)
2025-09-12 18:58:38.859 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Sent an 'adminMessage' to this workflow (clients_connections_details) about this client connection: ProducerRecord(topic=devcloud_clients_connections_details, partition=null, headers=RecordHeaders(headers = [], isReadOnly = true), key=market_price_list_updated|DPDA_Market_Price_Consumer|CA-JT26LR3, value={"clientId": "DPDA_Market_Price_Consumer", "workflowId": "market_price_list_updated", "connectionDescriptor": {"region": "aws-us-east-1", "env": "devcloud", "hostname": "CA-JT26LR3", "port": -1, "pid": 2720, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757717918124", "endDateTime": null, "error": null}, timestamp=null)
2025-09-12 18:58:39.241 [main] WARN  [] c.m.d.m.common.impl.KafkaFactory -- This method is being deprecated, we should not use it going forward! Please use other overloaded methods. Done building fully configured consumer (org.apache.kafka.clients.consumer.KafkaConsumer@f1a5005). 
Morningstar custom properties:
	{tasks_number_tag=1, serde_pojo_class_name_tag=com.morningstar.data.domain.eod.events.MarketPriceEvent, topic_name_tag=devcloud_market_price_list_updated}. 
This Consumer Connection Details:
	{"clientId": "DPDA_Market_Price_Consumer", "workflowId": "market_price_list_updated", "connectionDescriptor": {"region": "aws-us-east-1", "env": "devcloud", "hostname": "CA-JT26LR3", "port": -1, "pid": 2720, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757717918124", "endDateTime": null, "error": null}
2025-09-12 18:58:39.254 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- workflow market_price_list_updated enable.auto.commit is configured as false
2025-09-12 18:58:39.255 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- Done initialize this 'RunnableConsumer' - Details:
	Workflow: market_price_list_updated
	Overrides: [{enable.auto.commit=false, serde_pojo_class_name_tag=com.morningstar.data.domain.eod.events.MarketPriceEvent, group.id=dpda_market_price_group, auto.offset.reset=earliest, client.id=DPDA_Market_Price_Consumer}]
2025-09-12 18:58:39.255 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Done building fully configured consumer (com.morningstar.dp.messaging.common.impl.RunnableConsumer@66b16770)
2025-09-12 18:58:39.258 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- [DEPRECATED-Should not be here] - Done setting a system property for serde class (key:serde_pojo_class_name_tag, value:com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent)
2025-09-12 18:58:39.258 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent, codecName: null)
2025-09-12 18:58:39.261 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- This generic serde is using (serdePojoClassName: class com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent, codecName: null)
2025-09-12 18:58:39.261 [main] WARN  [] c.m.d.m.common.impl.KafkaFactory -- client id DPDA_Market_Price_Consumer already exists, will change it to DPDA_Market_Price_Consumer-0
2025-09-12 18:58:39.267 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 18:58:39.268 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- This generic serde is using (serdePojoClassName: class com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 18:58:39.268 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- About to send an 'adminMessage' to this workflow (clients_connections_details) about this client connection: ProducerRecord(topic=devcloud_clients_connections_details, partition=null, headers=RecordHeaders(headers = [], isReadOnly = false), key=market_price_enriched_event|DPDA_Market_Price_Consumer-0|CA-JT26LR3, value={"clientId": "DPDA_Market_Price_Consumer-0", "workflowId": "market_price_enriched_event", "connectionDescriptor": {"region": "aws-us-east-1", "env": "devcloud", "hostname": "CA-JT26LR3", "port": -1, "pid": 2720, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757717919267", "endDateTime": null, "error": null}, timestamp=null)
2025-09-12 18:58:39.268 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- This is for auditing only so no issue to worry about. Will not produce into the kafka-Plugin admin topic (clients_connections_details) for now.
2025-09-12 18:58:39.268 [main] WARN  [] c.m.d.m.common.impl.KafkaFactory -- This method is being deprecated, we should not use it going forward! Please use other overloaded methods. Done building fully configured consumer (org.apache.kafka.clients.consumer.KafkaConsumer@3af8f1ef). 
Morningstar custom properties:
	{tasks_number_tag=1, serde_pojo_class_name_tag=com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent, topic_name_tag=devcloud_market_price_enriched_event}. 
This Consumer Connection Details:
	{"clientId": "DPDA_Market_Price_Consumer-0", "workflowId": "market_price_enriched_event", "connectionDescriptor": {"region": "aws-us-east-1", "env": "devcloud", "hostname": "CA-JT26LR3", "port": -1, "pid": 2720, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757717919267", "endDateTime": null, "error": null}
2025-09-12 18:58:39.268 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- workflow market_price_enriched_event enable.auto.commit is configured as false
2025-09-12 18:58:39.268 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- Done initialize this 'RunnableConsumer' - Details:
	Workflow: market_price_enriched_event
	Overrides: [{enable.auto.commit=false, serde_pojo_class_name_tag=com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent, group.id=dpda_market_price_group, auto.offset.reset=earliest, client.id=DPDA_Market_Price_Consumer}]
2025-09-12 18:58:39.269 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Done building fully configured consumer (com.morningstar.dp.messaging.common.impl.RunnableConsumer@393e6bdd)
2025-09-12 18:58:39.722 [main] INFO  [] c.m.m.MartKafkaConsumerApplication -- Started MartKafkaConsumerApplication in 5.317 seconds (process running for 5.928)
2025-09-12 18:58:39.724 [main] INFO  [] c.m.m.c.AbstractMessageConsumer -- Starting Kafka message consumer for topic: market_price_enriched_event
2025-09-12 18:58:39.725 [main] INFO  [] c.m.m.c.AbstractMessageConsumer -- Kafka message consumer started successfully
2025-09-12 18:59:17.385 [runnable-consumer-0] INFO  [75070a48-4804-43aa-a65b-1edc9f617fb4] c.m.m.c.KafkaPluginConsumerEndpoint -- topic: devcloud_market_price_enriched_event, record partition 0, offset: 39, key: null, pollId: 75070a48-4804-43aa-a65b-1edc9f617fb4, value: {"market_price_data_list": [{"performance_id": "PERF_0", "investment_type": "Stock", "date": 2025-09-09, "high": 100.00000000000000000, "low": 90.00000000000000000, "open": 95.00000000000000000, "close": 98.00000000000000000, "bid": null, "mid": null, "ask": null, "currency_code": "USD", "traded_volume": 1000000.00000000000000000, "copy_over_reason": "PRICE"}], "exchange_detail": null, "batch_metadata": null, "event_timestamp_utc": 2025-09-12T22:54:53.494Z}
2025-09-12 18:59:17.386 [runnable-consumer-0] INFO  [75070a48-4804-43aa-a65b-1edc9f617fb4] c.m.m.c.KafkaPluginConsumerEndpoint -- topic: devcloud_market_price_enriched_event, record partition 0, offset: 40, key: null, pollId: 75070a48-4804-43aa-a65b-1edc9f617fb4, value: {"market_price_data_list": [{"performance_id": "PERF_0", "investment_type": "Stock", "date": 2025-09-09, "high": 100.00000000000000000, "low": 90.00000000000000000, "open": 95.00000000000000000, "close": 98.00000000000000000, "bid": null, "mid": null, "ask": null, "currency_code": "USD", "traded_volume": 1000000.00000000000000000, "copy_over_reason": "PRICE"}], "exchange_detail": null, "batch_metadata": null, "event_timestamp_utc": 2025-09-12T22:57:45.480Z}
2025-09-12 18:59:17.386 [runnable-consumer-0] INFO  [75070a48-4804-43aa-a65b-1edc9f617fb4] c.m.m.c.KafkaPluginConsumerEndpoint -- topic: devcloud_market_price_enriched_event, record partition 0, offset: 41, key: null, pollId: 75070a48-4804-43aa-a65b-1edc9f617fb4, value: {"market_price_data_list": [{"performance_id": "PERF_0", "investment_type": "Stock", "date": 2025-09-09, "high": 100.00000000000000000, "low": 90.00000000000000000, "open": 95.00000000000000000, "close": 98.00000000000000000, "bid": null, "mid": null, "ask": null, "currency_code": "USD", "traded_volume": 1000000.00000000000000000, "copy_over_reason": "PRICE"}], "exchange_detail": null, "batch_metadata": null, "event_timestamp_utc": 2025-09-12T22:58:22.942Z}
2025-09-12 18:59:30.494 [runnable-consumer-0] INFO  [75070a48-4804-43aa-a65b-1edc9f617fb4] c.m.m.c.AbstractMessageConsumer -- Received 3 messages with pollId: 75070a48-4804-43aa-a65b-1edc9f617fb4
2025-09-12 18:59:30.494 [runnable-consumer-0] INFO  [75070a48-4804-43aa-a65b-1edc9f617fb4] c.m.m.c.MarketPriceEnrichedConsumer -- MarketPriceEnrichedConsumer Processing 3 messages with pollId: 75070a48-4804-43aa-a65b-1edc9f617fb4
2025-09-12 18:59:30.500 [runnable-consumer-0] ERROR [75070a48-4804-43aa-a65b-1edc9f617fb4] c.m.m.c.AbstractMessageConsumer -- Default error handling: failed to process 3 messages, pollId: 75070a48-4804-43aa-a65b-1edc9f617fb4
java.util.concurrent.CompletionException: java.lang.RuntimeException: MarketPriceEnrichedConsumer: Failed to process message
	at java.base/java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:315)
	at java.base/java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:320)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1807)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.lang.RuntimeException: MarketPriceEnrichedConsumer: Failed to process message
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.processMessage(MarketPriceEnrichedConsumer.java:55)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$0(MarketPriceEnrichedConsumer.java:39)
	at com.morningstar.martkafkaconsumer.util.LoggingContext.withTraceId(LoggingContext.java:31)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$1(MarketPriceEnrichedConsumer.java:39)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1804)
	... 4 common frames omitted
2025-09-12 18:59:36.437 [runnable-consumer-0] ERROR [] c.m.d.m.common.impl.RunnableConsumer -- Got an exception while trying to notify a listener with new payload(s)
java.util.concurrent.CompletionException: java.lang.RuntimeException: MarketPriceEnrichedConsumer: Failed to process message
	at java.base/java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:315)
	at java.base/java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:320)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1807)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.lang.RuntimeException: MarketPriceEnrichedConsumer: Failed to process message
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.processMessage(MarketPriceEnrichedConsumer.java:55)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$0(MarketPriceEnrichedConsumer.java:39)
	at com.morningstar.martkafkaconsumer.util.LoggingContext.withTraceId(LoggingContext.java:31)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$1(MarketPriceEnrichedConsumer.java:39)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1804)
	... 4 common frames omitted
2025-09-12 18:59:36.437 [runnable-consumer-0] ERROR [] c.m.m.c.KafkaPluginConsumerEndpoint -- Received IllegalStateException while consuming from kafka
java.util.concurrent.CompletionException: java.lang.RuntimeException: MarketPriceEnrichedConsumer: Failed to process message
	at java.base/java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:315)
	at java.base/java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:320)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1807)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.lang.RuntimeException: MarketPriceEnrichedConsumer: Failed to process message
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.processMessage(MarketPriceEnrichedConsumer.java:55)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$0(MarketPriceEnrichedConsumer.java:39)
	at com.morningstar.martkafkaconsumer.util.LoggingContext.withTraceId(LoggingContext.java:31)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$1(MarketPriceEnrichedConsumer.java:39)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1804)
	... 4 common frames omitted
2025-09-12 19:01:24.209 [runnable-consumer-0] INFO  [3186a796-be23-4a5b-8d8f-98c4f34e2c96] c.m.m.c.KafkaPluginConsumerEndpoint -- topic: devcloud_market_price_enriched_event, record partition 0, offset: 42, key: null, pollId: 3186a796-be23-4a5b-8d8f-98c4f34e2c96, value: {"market_price_data_list": [{"performance_id": "PERF_0", "investment_type": "Stock", "date": 2025-09-09, "high": 100.00000000000000000, "low": 90.00000000000000000, "open": 95.00000000000000000, "close": 98.00000000000000000, "bid": null, "mid": null, "ask": null, "currency_code": "USD", "traded_volume": 1000000.00000000000000000, "copy_over_reason": "PRICE"}], "exchange_detail": null, "batch_metadata": null, "event_timestamp_utc": 2025-09-12T23:01:23.504Z}
2025-09-12 19:01:24.211 [runnable-consumer-0] INFO  [3186a796-be23-4a5b-8d8f-98c4f34e2c96] c.m.m.c.AbstractMessageConsumer -- Received 1 messages with pollId: 3186a796-be23-4a5b-8d8f-98c4f34e2c96
2025-09-12 19:01:24.211 [runnable-consumer-0] INFO  [3186a796-be23-4a5b-8d8f-98c4f34e2c96] c.m.m.c.MarketPriceEnrichedConsumer -- MarketPriceEnrichedConsumer Processing 1 messages with pollId: 3186a796-be23-4a5b-8d8f-98c4f34e2c96
2025-09-12 19:01:24.212 [runnable-consumer-0] ERROR [3186a796-be23-4a5b-8d8f-98c4f34e2c96] c.m.m.c.AbstractMessageConsumer -- Default error handling: failed to process 1 messages, pollId: 3186a796-be23-4a5b-8d8f-98c4f34e2c96
java.util.concurrent.CompletionException: java.lang.RuntimeException: MarketPriceEnrichedConsumer: Failed to process message
	at java.base/java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:315)
	at java.base/java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:320)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1807)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.lang.RuntimeException: MarketPriceEnrichedConsumer: Failed to process message
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.processMessage(MarketPriceEnrichedConsumer.java:55)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$0(MarketPriceEnrichedConsumer.java:39)
	at com.morningstar.martkafkaconsumer.util.LoggingContext.withTraceId(LoggingContext.java:31)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$1(MarketPriceEnrichedConsumer.java:39)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1804)
	... 4 common frames omitted
2025-09-12 19:01:24.213 [runnable-consumer-0] ERROR [] c.m.d.m.common.impl.RunnableConsumer -- Got an exception while trying to notify a listener with new payload(s)
java.util.concurrent.CompletionException: java.lang.RuntimeException: MarketPriceEnrichedConsumer: Failed to process message
	at java.base/java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:315)
	at java.base/java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:320)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1807)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.lang.RuntimeException: MarketPriceEnrichedConsumer: Failed to process message
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.processMessage(MarketPriceEnrichedConsumer.java:55)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$0(MarketPriceEnrichedConsumer.java:39)
	at com.morningstar.martkafkaconsumer.util.LoggingContext.withTraceId(LoggingContext.java:31)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$1(MarketPriceEnrichedConsumer.java:39)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1804)
	... 4 common frames omitted
2025-09-12 19:01:24.213 [runnable-consumer-0] ERROR [] c.m.m.c.KafkaPluginConsumerEndpoint -- Received IllegalStateException while consuming from kafka
java.util.concurrent.CompletionException: java.lang.RuntimeException: MarketPriceEnrichedConsumer: Failed to process message
	at java.base/java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:315)
	at java.base/java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:320)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1807)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.lang.RuntimeException: MarketPriceEnrichedConsumer: Failed to process message
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.processMessage(MarketPriceEnrichedConsumer.java:55)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$0(MarketPriceEnrichedConsumer.java:39)
	at com.morningstar.martkafkaconsumer.util.LoggingContext.withTraceId(LoggingContext.java:31)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$1(MarketPriceEnrichedConsumer.java:39)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1804)
	... 4 common frames omitted
2025-09-12 19:03:16.352 [runnable-consumer-0] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- Trying to close Kafka consumer, RunnableConsumer.isRunning: false
2025-09-12 19:03:22.970 [main] INFO  [] c.m.m.security.AuthInitializer -- [Authentication] Keytab: C:\Users\<USER>\AppData\Local\Temp\service-keytab-1173887922854551039.keytab
2025-09-12 19:03:22.973 [main] INFO  [] c.m.m.security.AuthInitializer -- [Authentication] JAAS conf: C:\Users\<USER>\AppData\Local\Temp\jaas-6398874020851229582.conf
2025-09-12 19:03:22.974 [main] INFO  [] c.m.m.security.AuthInitializer -- [Authentication] krb5 conf: C:\Users\<USER>\AppData\Local\Temp\krb5-5216208360594985215.conf
2025-09-12 19:03:22.979 [main] INFO  [] c.m.m.MartKafkaConsumerApplication -- Starting MartKafkaConsumerApplication using Java 17.0.9 with PID 35720 (C:\Zeng\dataac\msstash\mart-kafka-consumer\target\classes started by wzeng1 in C:\Zeng\dataac\msstash\mart-kafka-consumer)
2025-09-12 19:03:22.980 [main] DEBUG [] c.m.m.MartKafkaConsumerApplication -- Running with Spring Boot v3.5.5, Spring v6.2.10
2025-09-12 19:03:22.981 [main] INFO  [] c.m.m.MartKafkaConsumerApplication -- The following 1 profile is active: "dev"
2025-09-12 19:03:23.297 [main] INFO  [] o.s.d.r.c.RepositoryConfigurationDelegate -- Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-12 19:03:23.299 [main] INFO  [] o.s.d.r.c.RepositoryConfigurationDelegate -- Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-12 19:03:23.324 [main] INFO  [] o.s.d.r.c.RepositoryConfigurationDelegate -- Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces.
2025-09-12 19:03:23.464 [main] INFO  [] c.m.m.config.ExecutorConfig -- Created MarketPrice ExecutorService with 20 threads
2025-09-12 19:03:24.798 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Kafka factory fully initialized with region (aws-us-east-1) and env (devcloud)
2025-09-12 19:03:24.810 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- [DEPRECATED-Should not be here] - Done setting a system property for serde class (key:serde_pojo_class_name_tag, value:com.morningstar.data.domain.eod.events.MarketPriceEvent)
2025-09-12 19:03:24.812 [main] INFO  [] c.m.d.m.c.s.a.AvroNoSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.eod.events.MarketPriceEvent, codecName: null)
2025-09-12 19:03:25.053 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 19:03:25.053 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- This generic serde is using (serdePojoClassName: class com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 19:03:25.145 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- About to send an 'adminMessage' to this workflow (clients_connections_details) about this client connection: ProducerRecord(topic=devcloud_clients_connections_details, partition=null, headers=RecordHeaders(headers = [], isReadOnly = false), key=market_price_list_updated|DPDA_Market_Price_Consumer|CA-JT26LR3, value={"clientId": "DPDA_Market_Price_Consumer", "workflowId": "market_price_list_updated", "connectionDescriptor": {"region": "aws-us-east-1", "env": "devcloud", "hostname": "CA-JT26LR3", "port": -1, "pid": 35720, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757718205050", "endDateTime": null, "error": null}, timestamp=null)
2025-09-12 19:03:25.754 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Sent an 'adminMessage' to this workflow (clients_connections_details) about this client connection: ProducerRecord(topic=devcloud_clients_connections_details, partition=null, headers=RecordHeaders(headers = [], isReadOnly = true), key=market_price_list_updated|DPDA_Market_Price_Consumer|CA-JT26LR3, value={"clientId": "DPDA_Market_Price_Consumer", "workflowId": "market_price_list_updated", "connectionDescriptor": {"region": "aws-us-east-1", "env": "devcloud", "hostname": "CA-JT26LR3", "port": -1, "pid": 35720, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757718205050", "endDateTime": null, "error": null}, timestamp=null)
2025-09-12 19:03:26.118 [main] WARN  [] c.m.d.m.common.impl.KafkaFactory -- This method is being deprecated, we should not use it going forward! Please use other overloaded methods. Done building fully configured consumer (org.apache.kafka.clients.consumer.KafkaConsumer@41861964). 
Morningstar custom properties:
	{tasks_number_tag=1, serde_pojo_class_name_tag=com.morningstar.data.domain.eod.events.MarketPriceEvent, topic_name_tag=devcloud_market_price_list_updated}. 
This Consumer Connection Details:
	{"clientId": "DPDA_Market_Price_Consumer", "workflowId": "market_price_list_updated", "connectionDescriptor": {"region": "aws-us-east-1", "env": "devcloud", "hostname": "CA-JT26LR3", "port": -1, "pid": 35720, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757718205050", "endDateTime": null, "error": null}
2025-09-12 19:03:26.126 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- workflow market_price_list_updated enable.auto.commit is configured as false
2025-09-12 19:03:26.127 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- Done initialize this 'RunnableConsumer' - Details:
	Workflow: market_price_list_updated
	Overrides: [{enable.auto.commit=false, serde_pojo_class_name_tag=com.morningstar.data.domain.eod.events.MarketPriceEvent, group.id=dpda_market_price_group, auto.offset.reset=earliest, client.id=DPDA_Market_Price_Consumer}]
2025-09-12 19:03:26.128 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Done building fully configured consumer (com.morningstar.dp.messaging.common.impl.RunnableConsumer@7a6433f3)
2025-09-12 19:03:26.131 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- [DEPRECATED-Should not be here] - Done setting a system property for serde class (key:serde_pojo_class_name_tag, value:com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent)
2025-09-12 19:03:26.131 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent, codecName: null)
2025-09-12 19:03:26.134 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- This generic serde is using (serdePojoClassName: class com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent, codecName: null)
2025-09-12 19:03:26.135 [main] WARN  [] c.m.d.m.common.impl.KafkaFactory -- client id DPDA_Market_Price_Consumer already exists, will change it to DPDA_Market_Price_Consumer-0
2025-09-12 19:03:26.141 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 19:03:26.141 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- This generic serde is using (serdePojoClassName: class com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 19:03:26.141 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- About to send an 'adminMessage' to this workflow (clients_connections_details) about this client connection: ProducerRecord(topic=devcloud_clients_connections_details, partition=null, headers=RecordHeaders(headers = [], isReadOnly = false), key=market_price_enriched_event|DPDA_Market_Price_Consumer-0|CA-JT26LR3, value={"clientId": "DPDA_Market_Price_Consumer-0", "workflowId": "market_price_enriched_event", "connectionDescriptor": {"region": "aws-us-east-1", "env": "devcloud", "hostname": "CA-JT26LR3", "port": -1, "pid": 35720, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757718206141", "endDateTime": null, "error": null}, timestamp=null)
2025-09-12 19:03:26.141 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- This is for auditing only so no issue to worry about. Will not produce into the kafka-Plugin admin topic (clients_connections_details) for now.
2025-09-12 19:03:26.141 [main] WARN  [] c.m.d.m.common.impl.KafkaFactory -- This method is being deprecated, we should not use it going forward! Please use other overloaded methods. Done building fully configured consumer (org.apache.kafka.clients.consumer.KafkaConsumer@3b07b706). 
Morningstar custom properties:
	{tasks_number_tag=1, serde_pojo_class_name_tag=com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent, topic_name_tag=devcloud_market_price_enriched_event}. 
This Consumer Connection Details:
	{"clientId": "DPDA_Market_Price_Consumer-0", "workflowId": "market_price_enriched_event", "connectionDescriptor": {"region": "aws-us-east-1", "env": "devcloud", "hostname": "CA-JT26LR3", "port": -1, "pid": 35720, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757718206141", "endDateTime": null, "error": null}
2025-09-12 19:03:26.141 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- workflow market_price_enriched_event enable.auto.commit is configured as false
2025-09-12 19:03:26.142 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- Done initialize this 'RunnableConsumer' - Details:
	Workflow: market_price_enriched_event
	Overrides: [{enable.auto.commit=false, serde_pojo_class_name_tag=com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent, group.id=dpda_market_price_group, auto.offset.reset=earliest, client.id=DPDA_Market_Price_Consumer}]
2025-09-12 19:03:26.142 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Done building fully configured consumer (com.morningstar.dp.messaging.common.impl.RunnableConsumer@7b4185ea)
2025-09-12 19:03:26.530 [main] INFO  [] c.m.m.MartKafkaConsumerApplication -- Started MartKafkaConsumerApplication in 5.044 seconds (process running for 5.551)
2025-09-12 19:03:26.532 [main] INFO  [] c.m.m.c.AbstractMessageConsumer -- Starting Kafka message consumer for topic: market_price_enriched_event
2025-09-12 19:03:26.533 [main] INFO  [] c.m.m.c.AbstractMessageConsumer -- Kafka message consumer started successfully
2025-09-12 19:03:30.820 [runnable-consumer-0] INFO  [5728e92f-dcd9-4f16-af66-f7cb18767119] c.m.m.c.KafkaPluginConsumerEndpoint -- topic: devcloud_market_price_enriched_event, record partition 0, offset: 39, key: null, pollId: 5728e92f-dcd9-4f16-af66-f7cb18767119, value: {"market_price_data_list": [{"performance_id": "PERF_0", "investment_type": "Stock", "date": 2025-09-09, "high": 100.00000000000000000, "low": 90.00000000000000000, "open": 95.00000000000000000, "close": 98.00000000000000000, "bid": null, "mid": null, "ask": null, "currency_code": "USD", "traded_volume": 1000000.00000000000000000, "copy_over_reason": "PRICE"}], "exchange_detail": null, "batch_metadata": null, "event_timestamp_utc": 2025-09-12T22:54:53.494Z}
2025-09-12 19:03:30.820 [runnable-consumer-0] INFO  [5728e92f-dcd9-4f16-af66-f7cb18767119] c.m.m.c.KafkaPluginConsumerEndpoint -- topic: devcloud_market_price_enriched_event, record partition 0, offset: 40, key: null, pollId: 5728e92f-dcd9-4f16-af66-f7cb18767119, value: {"market_price_data_list": [{"performance_id": "PERF_0", "investment_type": "Stock", "date": 2025-09-09, "high": 100.00000000000000000, "low": 90.00000000000000000, "open": 95.00000000000000000, "close": 98.00000000000000000, "bid": null, "mid": null, "ask": null, "currency_code": "USD", "traded_volume": 1000000.00000000000000000, "copy_over_reason": "PRICE"}], "exchange_detail": null, "batch_metadata": null, "event_timestamp_utc": 2025-09-12T22:57:45.480Z}
2025-09-12 19:03:30.821 [runnable-consumer-0] INFO  [5728e92f-dcd9-4f16-af66-f7cb18767119] c.m.m.c.KafkaPluginConsumerEndpoint -- topic: devcloud_market_price_enriched_event, record partition 0, offset: 41, key: null, pollId: 5728e92f-dcd9-4f16-af66-f7cb18767119, value: {"market_price_data_list": [{"performance_id": "PERF_0", "investment_type": "Stock", "date": 2025-09-09, "high": 100.00000000000000000, "low": 90.00000000000000000, "open": 95.00000000000000000, "close": 98.00000000000000000, "bid": null, "mid": null, "ask": null, "currency_code": "USD", "traded_volume": 1000000.00000000000000000, "copy_over_reason": "PRICE"}], "exchange_detail": null, "batch_metadata": null, "event_timestamp_utc": 2025-09-12T22:58:22.942Z}
2025-09-12 19:03:30.821 [runnable-consumer-0] INFO  [5728e92f-dcd9-4f16-af66-f7cb18767119] c.m.m.c.KafkaPluginConsumerEndpoint -- topic: devcloud_market_price_enriched_event, record partition 0, offset: 42, key: null, pollId: 5728e92f-dcd9-4f16-af66-f7cb18767119, value: {"market_price_data_list": [{"performance_id": "PERF_0", "investment_type": "Stock", "date": 2025-09-09, "high": 100.00000000000000000, "low": 90.00000000000000000, "open": 95.00000000000000000, "close": 98.00000000000000000, "bid": null, "mid": null, "ask": null, "currency_code": "USD", "traded_volume": 1000000.00000000000000000, "copy_over_reason": "PRICE"}], "exchange_detail": null, "batch_metadata": null, "event_timestamp_utc": 2025-09-12T23:01:23.504Z}
2025-09-12 19:03:30.821 [runnable-consumer-0] INFO  [5728e92f-dcd9-4f16-af66-f7cb18767119] c.m.m.c.AbstractMessageConsumer -- Received 4 messages with pollId: 5728e92f-dcd9-4f16-af66-f7cb18767119
2025-09-12 19:03:30.821 [runnable-consumer-0] INFO  [5728e92f-dcd9-4f16-af66-f7cb18767119] c.m.m.c.MarketPriceEnrichedConsumer -- MarketPriceEnrichedConsumer Processing 4 messages with pollId: 5728e92f-dcd9-4f16-af66-f7cb18767119
2025-09-12 19:03:30.823 [runnable-consumer-0] ERROR [5728e92f-dcd9-4f16-af66-f7cb18767119] c.m.m.c.AbstractMessageConsumer -- Default error handling: failed to process 4 messages, pollId: 5728e92f-dcd9-4f16-af66-f7cb18767119
java.util.concurrent.CompletionException: java.lang.RuntimeException: MarketPriceEnrichedConsumer: Failed to process message
	at java.base/java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:315)
	at java.base/java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:320)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1807)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.lang.RuntimeException: MarketPriceEnrichedConsumer: Failed to process message
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.processMessage(MarketPriceEnrichedConsumer.java:55)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$0(MarketPriceEnrichedConsumer.java:39)
	at com.morningstar.martkafkaconsumer.util.LoggingContext.withTraceId(LoggingContext.java:31)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$1(MarketPriceEnrichedConsumer.java:39)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	... 3 common frames omitted
2025-09-12 19:03:30.825 [runnable-consumer-0] ERROR [] c.m.d.m.common.impl.RunnableConsumer -- Got an exception while trying to notify a listener with new payload(s)
java.util.concurrent.CompletionException: java.lang.RuntimeException: MarketPriceEnrichedConsumer: Failed to process message
	at java.base/java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:315)
	at java.base/java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:320)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1807)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.lang.RuntimeException: MarketPriceEnrichedConsumer: Failed to process message
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.processMessage(MarketPriceEnrichedConsumer.java:55)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$0(MarketPriceEnrichedConsumer.java:39)
	at com.morningstar.martkafkaconsumer.util.LoggingContext.withTraceId(LoggingContext.java:31)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$1(MarketPriceEnrichedConsumer.java:39)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	... 3 common frames omitted
2025-09-12 19:03:30.825 [runnable-consumer-0] ERROR [] c.m.m.c.KafkaPluginConsumerEndpoint -- Received IllegalStateException while consuming from kafka
java.util.concurrent.CompletionException: java.lang.RuntimeException: MarketPriceEnrichedConsumer: Failed to process message
	at java.base/java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:315)
	at java.base/java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:320)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1807)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.lang.RuntimeException: MarketPriceEnrichedConsumer: Failed to process message
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.processMessage(MarketPriceEnrichedConsumer.java:55)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$0(MarketPriceEnrichedConsumer.java:39)
	at com.morningstar.martkafkaconsumer.util.LoggingContext.withTraceId(LoggingContext.java:31)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$1(MarketPriceEnrichedConsumer.java:39)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	... 3 common frames omitted
2025-09-12 19:03:57.789 [runnable-consumer-0] INFO  [125da0fb-cef2-492d-9e73-48e88d411655] c.m.m.c.KafkaPluginConsumerEndpoint -- topic: devcloud_market_price_enriched_event, record partition 0, offset: 43, key: null, pollId: 125da0fb-cef2-492d-9e73-48e88d411655, value: {"market_price_data_list": [{"performance_id": "PERF_0", "investment_type": "Stock", "date": 2025-09-09, "high": 100.00000000000000000, "low": 90.00000000000000000, "open": 95.00000000000000000, "close": 98.00000000000000000, "bid": null, "mid": null, "ask": null, "currency_code": "USD", "traded_volume": 1000000.00000000000000000, "copy_over_reason": "PRICE"}], "exchange_detail": null, "batch_metadata": null, "event_timestamp_utc": 2025-09-12T23:03:57.347Z}
2025-09-12 19:03:57.790 [runnable-consumer-0] INFO  [125da0fb-cef2-492d-9e73-48e88d411655] c.m.m.c.AbstractMessageConsumer -- Received 1 messages with pollId: 125da0fb-cef2-492d-9e73-48e88d411655
2025-09-12 19:03:57.790 [runnable-consumer-0] INFO  [125da0fb-cef2-492d-9e73-48e88d411655] c.m.m.c.MarketPriceEnrichedConsumer -- MarketPriceEnrichedConsumer Processing 1 messages with pollId: 125da0fb-cef2-492d-9e73-48e88d411655
2025-09-12 19:03:57.792 [runnable-consumer-0] ERROR [125da0fb-cef2-492d-9e73-48e88d411655] c.m.m.c.AbstractMessageConsumer -- Default error handling: failed to process 1 messages, pollId: 125da0fb-cef2-492d-9e73-48e88d411655
java.util.concurrent.CompletionException: java.lang.RuntimeException: MarketPriceEnrichedConsumer: Failed to process message
	at java.base/java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:315)
	at java.base/java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:320)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1807)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.lang.RuntimeException: MarketPriceEnrichedConsumer: Failed to process message
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.processMessage(MarketPriceEnrichedConsumer.java:55)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$0(MarketPriceEnrichedConsumer.java:39)
	at com.morningstar.martkafkaconsumer.util.LoggingContext.withTraceId(LoggingContext.java:31)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$1(MarketPriceEnrichedConsumer.java:39)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	... 3 common frames omitted
2025-09-12 19:03:57.793 [runnable-consumer-0] ERROR [] c.m.d.m.common.impl.RunnableConsumer -- Got an exception while trying to notify a listener with new payload(s)
java.util.concurrent.CompletionException: java.lang.RuntimeException: MarketPriceEnrichedConsumer: Failed to process message
	at java.base/java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:315)
	at java.base/java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:320)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1807)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.lang.RuntimeException: MarketPriceEnrichedConsumer: Failed to process message
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.processMessage(MarketPriceEnrichedConsumer.java:55)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$0(MarketPriceEnrichedConsumer.java:39)
	at com.morningstar.martkafkaconsumer.util.LoggingContext.withTraceId(LoggingContext.java:31)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$1(MarketPriceEnrichedConsumer.java:39)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	... 3 common frames omitted
2025-09-12 19:03:57.793 [runnable-consumer-0] ERROR [] c.m.m.c.KafkaPluginConsumerEndpoint -- Received IllegalStateException while consuming from kafka
java.util.concurrent.CompletionException: java.lang.RuntimeException: MarketPriceEnrichedConsumer: Failed to process message
	at java.base/java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:315)
	at java.base/java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:320)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1807)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.lang.RuntimeException: MarketPriceEnrichedConsumer: Failed to process message
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.processMessage(MarketPriceEnrichedConsumer.java:55)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$0(MarketPriceEnrichedConsumer.java:39)
	at com.morningstar.martkafkaconsumer.util.LoggingContext.withTraceId(LoggingContext.java:31)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$1(MarketPriceEnrichedConsumer.java:39)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	... 3 common frames omitted
2025-09-12 19:04:08.783 [runnable-consumer-0] INFO  [a8934686-91dc-48cb-900c-d4b0be68c68c] c.m.m.c.KafkaPluginConsumerEndpoint -- topic: devcloud_market_price_enriched_event, record partition 0, offset: 44, key: null, pollId: a8934686-91dc-48cb-900c-d4b0be68c68c, value: {"market_price_data_list": [{"performance_id": "PERF_0", "investment_type": "Stock", "date": 2025-09-09, "high": 100.00000000000000000, "low": 90.00000000000000000, "open": 95.00000000000000000, "close": 98.00000000000000000, "bid": null, "mid": null, "ask": null, "currency_code": "USD", "traded_volume": 1000000.00000000000000000, "copy_over_reason": "PRICE"}], "exchange_detail": null, "batch_metadata": null, "event_timestamp_utc": 2025-09-12T23:04:08.081Z}
2025-09-12 19:04:08.783 [runnable-consumer-0] INFO  [a8934686-91dc-48cb-900c-d4b0be68c68c] c.m.m.c.AbstractMessageConsumer -- Received 1 messages with pollId: a8934686-91dc-48cb-900c-d4b0be68c68c
2025-09-12 19:04:08.783 [runnable-consumer-0] INFO  [a8934686-91dc-48cb-900c-d4b0be68c68c] c.m.m.c.MarketPriceEnrichedConsumer -- MarketPriceEnrichedConsumer Processing 1 messages with pollId: a8934686-91dc-48cb-900c-d4b0be68c68c
2025-09-12 19:04:08.785 [runnable-consumer-0] ERROR [a8934686-91dc-48cb-900c-d4b0be68c68c] c.m.m.c.AbstractMessageConsumer -- Default error handling: failed to process 1 messages, pollId: a8934686-91dc-48cb-900c-d4b0be68c68c
java.util.concurrent.CompletionException: java.lang.RuntimeException: MarketPriceEnrichedConsumer: Failed to process message
	at java.base/java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:315)
	at java.base/java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:320)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1807)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.lang.RuntimeException: MarketPriceEnrichedConsumer: Failed to process message
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.processMessage(MarketPriceEnrichedConsumer.java:55)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$0(MarketPriceEnrichedConsumer.java:39)
	at com.morningstar.martkafkaconsumer.util.LoggingContext.withTraceId(LoggingContext.java:31)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$1(MarketPriceEnrichedConsumer.java:39)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	... 3 common frames omitted
2025-09-12 19:04:08.786 [runnable-consumer-0] ERROR [] c.m.d.m.common.impl.RunnableConsumer -- Got an exception while trying to notify a listener with new payload(s)
java.util.concurrent.CompletionException: java.lang.RuntimeException: MarketPriceEnrichedConsumer: Failed to process message
	at java.base/java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:315)
	at java.base/java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:320)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1807)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.lang.RuntimeException: MarketPriceEnrichedConsumer: Failed to process message
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.processMessage(MarketPriceEnrichedConsumer.java:55)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$0(MarketPriceEnrichedConsumer.java:39)
	at com.morningstar.martkafkaconsumer.util.LoggingContext.withTraceId(LoggingContext.java:31)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$1(MarketPriceEnrichedConsumer.java:39)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	... 3 common frames omitted
2025-09-12 19:04:08.786 [runnable-consumer-0] ERROR [] c.m.m.c.KafkaPluginConsumerEndpoint -- Received IllegalStateException while consuming from kafka
java.util.concurrent.CompletionException: java.lang.RuntimeException: MarketPriceEnrichedConsumer: Failed to process message
	at java.base/java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:315)
	at java.base/java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:320)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1807)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.lang.RuntimeException: MarketPriceEnrichedConsumer: Failed to process message
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.processMessage(MarketPriceEnrichedConsumer.java:55)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$0(MarketPriceEnrichedConsumer.java:39)
	at com.morningstar.martkafkaconsumer.util.LoggingContext.withTraceId(LoggingContext.java:31)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$1(MarketPriceEnrichedConsumer.java:39)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	... 3 common frames omitted
2025-09-12 19:09:47.172 [runnable-consumer-0] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- Trying to close Kafka consumer, RunnableConsumer.isRunning: false
2025-09-12 19:31:12.769 [main] INFO  [] c.m.m.security.AuthInitializer -- [Authentication] Keytab: C:\Users\<USER>\AppData\Local\Temp\service-keytab-491542022870624658.keytab
2025-09-12 19:31:12.771 [main] INFO  [] c.m.m.security.AuthInitializer -- [Authentication] JAAS conf: C:\Users\<USER>\AppData\Local\Temp\jaas-6545390891532418526.conf
2025-09-12 19:31:12.771 [main] INFO  [] c.m.m.security.AuthInitializer -- [Authentication] krb5 conf: C:\Users\<USER>\AppData\Local\Temp\krb5-4252185250184910235.conf
2025-09-12 19:31:12.773 [main] INFO  [] c.m.m.MartKafkaConsumerApplication -- Starting MartKafkaConsumerApplication using Java 17.0.9 with PID 7064 (C:\Zeng\dataac\msstash\mart-kafka-consumer\target\classes started by wzeng1 in C:\Zeng\dataac\msstash\mart-kafka-consumer)
2025-09-12 19:31:12.774 [main] DEBUG [] c.m.m.MartKafkaConsumerApplication -- Running with Spring Boot v3.5.5, Spring v6.2.10
2025-09-12 19:31:12.774 [main] INFO  [] c.m.m.MartKafkaConsumerApplication -- The following 1 profile is active: "dev"
2025-09-12 19:31:13.041 [main] INFO  [] o.s.d.r.c.RepositoryConfigurationDelegate -- Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-12 19:31:13.043 [main] INFO  [] o.s.d.r.c.RepositoryConfigurationDelegate -- Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-12 19:31:13.067 [main] INFO  [] o.s.d.r.c.RepositoryConfigurationDelegate -- Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
2025-09-12 19:31:13.192 [main] INFO  [] c.m.m.config.ExecutorConfig -- Created MarketPrice ExecutorService with 20 threads
2025-09-12 19:31:14.542 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Kafka factory fully initialized with region (aws-us-east-1) and env (devcloud)
2025-09-12 19:31:14.554 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- [DEPRECATED-Should not be here] - Done setting a system property for serde class (key:serde_pojo_class_name_tag, value:com.morningstar.data.domain.eod.events.MarketPriceEvent)
2025-09-12 19:31:14.556 [main] INFO  [] c.m.d.m.c.s.a.AvroNoSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.eod.events.MarketPriceEvent, codecName: null)
2025-09-12 19:31:14.788 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 19:31:14.790 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- This generic serde is using (serdePojoClassName: class com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 19:31:14.874 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- About to send an 'adminMessage' to this workflow (clients_connections_details) about this client connection: ProducerRecord(topic=devcloud_clients_connections_details, partition=null, headers=RecordHeaders(headers = [], isReadOnly = false), key=market_price_list_updated|DPDA_Market_Price_Consumer|CA-JT26LR3, value={"clientId": "DPDA_Market_Price_Consumer", "workflowId": "market_price_list_updated", "connectionDescriptor": {"region": "aws-us-east-1", "env": "devcloud", "hostname": "CA-JT26LR3", "port": -1, "pid": 7064, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757719874786", "endDateTime": null, "error": null}, timestamp=null)
2025-09-12 19:31:15.463 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Sent an 'adminMessage' to this workflow (clients_connections_details) about this client connection: ProducerRecord(topic=devcloud_clients_connections_details, partition=null, headers=RecordHeaders(headers = [], isReadOnly = true), key=market_price_list_updated|DPDA_Market_Price_Consumer|CA-JT26LR3, value={"clientId": "DPDA_Market_Price_Consumer", "workflowId": "market_price_list_updated", "connectionDescriptor": {"region": "aws-us-east-1", "env": "devcloud", "hostname": "CA-JT26LR3", "port": -1, "pid": 7064, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757719874786", "endDateTime": null, "error": null}, timestamp=null)
2025-09-12 19:31:15.815 [main] WARN  [] c.m.d.m.common.impl.KafkaFactory -- This method is being deprecated, we should not use it going forward! Please use other overloaded methods. Done building fully configured consumer (org.apache.kafka.clients.consumer.KafkaConsumer@a8e649e). 
Morningstar custom properties:
	{tasks_number_tag=1, serde_pojo_class_name_tag=com.morningstar.data.domain.eod.events.MarketPriceEvent, topic_name_tag=devcloud_market_price_list_updated}. 
This Consumer Connection Details:
	{"clientId": "DPDA_Market_Price_Consumer", "workflowId": "market_price_list_updated", "connectionDescriptor": {"region": "aws-us-east-1", "env": "devcloud", "hostname": "CA-JT26LR3", "port": -1, "pid": 7064, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757719874786", "endDateTime": null, "error": null}
2025-09-12 19:31:15.824 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- workflow market_price_list_updated enable.auto.commit is configured as false
2025-09-12 19:31:15.825 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- Done initialize this 'RunnableConsumer' - Details:
	Workflow: market_price_list_updated
	Overrides: [{enable.auto.commit=false, serde_pojo_class_name_tag=com.morningstar.data.domain.eod.events.MarketPriceEvent, group.id=dpda_market_price_group, auto.offset.reset=earliest, client.id=DPDA_Market_Price_Consumer}]
2025-09-12 19:31:15.825 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Done building fully configured consumer (com.morningstar.dp.messaging.common.impl.RunnableConsumer@454056b)
2025-09-12 19:31:15.827 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- [DEPRECATED-Should not be here] - Done setting a system property for serde class (key:serde_pojo_class_name_tag, value:com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent)
2025-09-12 19:31:15.828 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent, codecName: null)
2025-09-12 19:31:15.830 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- This generic serde is using (serdePojoClassName: class com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent, codecName: null)
2025-09-12 19:31:15.830 [main] WARN  [] c.m.d.m.common.impl.KafkaFactory -- client id DPDA_Market_Price_Consumer already exists, will change it to DPDA_Market_Price_Consumer-0
2025-09-12 19:31:15.835 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 19:31:15.835 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- This generic serde is using (serdePojoClassName: class com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 19:31:15.835 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- About to send an 'adminMessage' to this workflow (clients_connections_details) about this client connection: ProducerRecord(topic=devcloud_clients_connections_details, partition=null, headers=RecordHeaders(headers = [], isReadOnly = false), key=market_price_enriched_event|DPDA_Market_Price_Consumer-0|CA-JT26LR3, value={"clientId": "DPDA_Market_Price_Consumer-0", "workflowId": "market_price_enriched_event", "connectionDescriptor": {"region": "aws-us-east-1", "env": "devcloud", "hostname": "CA-JT26LR3", "port": -1, "pid": 7064, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757719875835", "endDateTime": null, "error": null}, timestamp=null)
2025-09-12 19:31:15.835 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- This is for auditing only so no issue to worry about. Will not produce into the kafka-Plugin admin topic (clients_connections_details) for now.
2025-09-12 19:31:15.835 [main] WARN  [] c.m.d.m.common.impl.KafkaFactory -- This method is being deprecated, we should not use it going forward! Please use other overloaded methods. Done building fully configured consumer (org.apache.kafka.clients.consumer.KafkaConsumer@48515dfc). 
Morningstar custom properties:
	{tasks_number_tag=1, serde_pojo_class_name_tag=com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent, topic_name_tag=devcloud_market_price_enriched_event}. 
This Consumer Connection Details:
	{"clientId": "DPDA_Market_Price_Consumer-0", "workflowId": "market_price_enriched_event", "connectionDescriptor": {"region": "aws-us-east-1", "env": "devcloud", "hostname": "CA-JT26LR3", "port": -1, "pid": 7064, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757719875835", "endDateTime": null, "error": null}
2025-09-12 19:31:15.836 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- workflow market_price_enriched_event enable.auto.commit is configured as false
2025-09-12 19:31:15.836 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- Done initialize this 'RunnableConsumer' - Details:
	Workflow: market_price_enriched_event
	Overrides: [{enable.auto.commit=false, serde_pojo_class_name_tag=com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent, group.id=dpda_market_price_group, auto.offset.reset=earliest, client.id=DPDA_Market_Price_Consumer}]
2025-09-12 19:31:15.836 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Done building fully configured consumer (com.morningstar.dp.messaging.common.impl.RunnableConsumer@561b39f4)
2025-09-12 19:31:16.193 [main] INFO  [] c.m.m.MartKafkaConsumerApplication -- Started MartKafkaConsumerApplication in 4.856 seconds (process running for 5.425)
2025-09-12 19:31:16.195 [main] INFO  [] c.m.m.c.AbstractMessageConsumer -- Starting Kafka message consumer for workflow: market_price_enriched_event
2025-09-12 19:31:16.196 [main] INFO  [] c.m.m.c.AbstractMessageConsumer -- Kafka message consumer started successfully
2025-09-12 19:31:20.457 [runnable-consumer-0] INFO  [9191e729-5014-4f28-bc64-c9ccaf52a341] c.m.m.c.KafkaPluginConsumerEndpoint -- topic: devcloud_market_price_enriched_event, record partition 0, offset: 39, key: null, pollId: 9191e729-5014-4f28-bc64-c9ccaf52a341, value: {"market_price_data_list": [{"performance_id": "PERF_0", "investment_type": "Stock", "date": 2025-09-09, "high": 100.00000000000000000, "low": 90.00000000000000000, "open": 95.00000000000000000, "close": 98.00000000000000000, "bid": null, "mid": null, "ask": null, "currency_code": "USD", "traded_volume": 1000000.00000000000000000, "copy_over_reason": "PRICE"}], "exchange_detail": null, "batch_metadata": null, "event_timestamp_utc": 2025-09-12T22:54:53.494Z}
2025-09-12 19:31:20.457 [runnable-consumer-0] INFO  [9191e729-5014-4f28-bc64-c9ccaf52a341] c.m.m.c.KafkaPluginConsumerEndpoint -- topic: devcloud_market_price_enriched_event, record partition 0, offset: 40, key: null, pollId: 9191e729-5014-4f28-bc64-c9ccaf52a341, value: {"market_price_data_list": [{"performance_id": "PERF_0", "investment_type": "Stock", "date": 2025-09-09, "high": 100.00000000000000000, "low": 90.00000000000000000, "open": 95.00000000000000000, "close": 98.00000000000000000, "bid": null, "mid": null, "ask": null, "currency_code": "USD", "traded_volume": 1000000.00000000000000000, "copy_over_reason": "PRICE"}], "exchange_detail": null, "batch_metadata": null, "event_timestamp_utc": 2025-09-12T22:57:45.480Z}
2025-09-12 19:31:20.458 [runnable-consumer-0] INFO  [9191e729-5014-4f28-bc64-c9ccaf52a341] c.m.m.c.KafkaPluginConsumerEndpoint -- topic: devcloud_market_price_enriched_event, record partition 0, offset: 41, key: null, pollId: 9191e729-5014-4f28-bc64-c9ccaf52a341, value: {"market_price_data_list": [{"performance_id": "PERF_0", "investment_type": "Stock", "date": 2025-09-09, "high": 100.00000000000000000, "low": 90.00000000000000000, "open": 95.00000000000000000, "close": 98.00000000000000000, "bid": null, "mid": null, "ask": null, "currency_code": "USD", "traded_volume": 1000000.00000000000000000, "copy_over_reason": "PRICE"}], "exchange_detail": null, "batch_metadata": null, "event_timestamp_utc": 2025-09-12T22:58:22.942Z}
2025-09-12 19:31:20.458 [runnable-consumer-0] INFO  [9191e729-5014-4f28-bc64-c9ccaf52a341] c.m.m.c.KafkaPluginConsumerEndpoint -- topic: devcloud_market_price_enriched_event, record partition 0, offset: 42, key: null, pollId: 9191e729-5014-4f28-bc64-c9ccaf52a341, value: {"market_price_data_list": [{"performance_id": "PERF_0", "investment_type": "Stock", "date": 2025-09-09, "high": 100.00000000000000000, "low": 90.00000000000000000, "open": 95.00000000000000000, "close": 98.00000000000000000, "bid": null, "mid": null, "ask": null, "currency_code": "USD", "traded_volume": 1000000.00000000000000000, "copy_over_reason": "PRICE"}], "exchange_detail": null, "batch_metadata": null, "event_timestamp_utc": 2025-09-12T23:01:23.504Z}
2025-09-12 19:31:20.458 [runnable-consumer-0] INFO  [9191e729-5014-4f28-bc64-c9ccaf52a341] c.m.m.c.KafkaPluginConsumerEndpoint -- topic: devcloud_market_price_enriched_event, record partition 0, offset: 43, key: null, pollId: 9191e729-5014-4f28-bc64-c9ccaf52a341, value: {"market_price_data_list": [{"performance_id": "PERF_0", "investment_type": "Stock", "date": 2025-09-09, "high": 100.00000000000000000, "low": 90.00000000000000000, "open": 95.00000000000000000, "close": 98.00000000000000000, "bid": null, "mid": null, "ask": null, "currency_code": "USD", "traded_volume": 1000000.00000000000000000, "copy_over_reason": "PRICE"}], "exchange_detail": null, "batch_metadata": null, "event_timestamp_utc": 2025-09-12T23:03:57.347Z}
2025-09-12 19:31:20.458 [runnable-consumer-0] INFO  [9191e729-5014-4f28-bc64-c9ccaf52a341] c.m.m.c.KafkaPluginConsumerEndpoint -- topic: devcloud_market_price_enriched_event, record partition 0, offset: 44, key: null, pollId: 9191e729-5014-4f28-bc64-c9ccaf52a341, value: {"market_price_data_list": [{"performance_id": "PERF_0", "investment_type": "Stock", "date": 2025-09-09, "high": 100.00000000000000000, "low": 90.00000000000000000, "open": 95.00000000000000000, "close": 98.00000000000000000, "bid": null, "mid": null, "ask": null, "currency_code": "USD", "traded_volume": 1000000.00000000000000000, "copy_over_reason": "PRICE"}], "exchange_detail": null, "batch_metadata": null, "event_timestamp_utc": 2025-09-12T23:04:08.081Z}
2025-09-12 19:31:20.458 [runnable-consumer-0] INFO  [9191e729-5014-4f28-bc64-c9ccaf52a341] c.m.m.c.AbstractMessageConsumer -- Received 6 messages with pollId: 9191e729-5014-4f28-bc64-c9ccaf52a341
2025-09-12 19:31:20.458 [runnable-consumer-0] INFO  [9191e729-5014-4f28-bc64-c9ccaf52a341] c.m.m.c.MarketPriceEnrichedConsumer -- MarketPriceEnrichedConsumer Processing 6 messages with pollId: 9191e729-5014-4f28-bc64-c9ccaf52a341
2025-09-12 19:31:20.459 [pool-3-thread-1] INFO  [9191e729-5014-4f28-bc64-c9ccaf52a341] c.m.m.service.MarketPriceDataService -- Storing market price data for investmentId: PERF_0, date: 2025-09-09
2025-09-12 19:31:20.459 [pool-3-thread-2] INFO  [9191e729-5014-4f28-bc64-c9ccaf52a341] c.m.m.service.MarketPriceDataService -- Storing market price data for investmentId: PERF_0, date: 2025-09-09
2025-09-12 19:31:20.460 [pool-3-thread-3] INFO  [9191e729-5014-4f28-bc64-c9ccaf52a341] c.m.m.service.MarketPriceDataService -- Storing market price data for investmentId: PERF_0, date: 2025-09-09
2025-09-12 19:31:20.460 [pool-3-thread-4] INFO  [9191e729-5014-4f28-bc64-c9ccaf52a341] c.m.m.service.MarketPriceDataService -- Storing market price data for investmentId: PERF_0, date: 2025-09-09
2025-09-12 19:31:20.460 [pool-3-thread-5] INFO  [9191e729-5014-4f28-bc64-c9ccaf52a341] c.m.m.service.MarketPriceDataService -- Storing market price data for investmentId: PERF_0, date: 2025-09-09
2025-09-12 19:31:20.460 [pool-3-thread-6] INFO  [9191e729-5014-4f28-bc64-c9ccaf52a341] c.m.m.service.MarketPriceDataService -- Storing market price data for investmentId: PERF_0, date: 2025-09-09
2025-09-12 19:31:22.480 [pool-3-thread-1] ERROR [9191e729-5014-4f28-bc64-c9ccaf52a341] c.m.m.c.MarketPriceEnrichedConsumer -- MarketPriceEnrichedConsumer: Failed to process MarketPriceDetail for performanceId: PERF_0, date: 2025-09-09
java.lang.RuntimeException: Test Exception
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.processMessage(MarketPriceEnrichedConsumer.java:58)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$0(MarketPriceEnrichedConsumer.java:39)
	at com.morningstar.martkafkaconsumer.util.LoggingContext.withTraceId(LoggingContext.java:31)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$1(MarketPriceEnrichedConsumer.java:39)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-09-12 19:31:22.482 [pool-3-thread-5] ERROR [9191e729-5014-4f28-bc64-c9ccaf52a341] c.m.m.c.MarketPriceEnrichedConsumer -- MarketPriceEnrichedConsumer: Failed to process MarketPriceDetail for performanceId: PERF_0, date: 2025-09-09
java.lang.RuntimeException: Test Exception
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.processMessage(MarketPriceEnrichedConsumer.java:58)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$0(MarketPriceEnrichedConsumer.java:39)
	at com.morningstar.martkafkaconsumer.util.LoggingContext.withTraceId(LoggingContext.java:31)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$1(MarketPriceEnrichedConsumer.java:39)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-09-12 19:31:22.488 [pool-3-thread-3] ERROR [9191e729-5014-4f28-bc64-c9ccaf52a341] c.m.m.c.MarketPriceEnrichedConsumer -- MarketPriceEnrichedConsumer: Failed to process MarketPriceDetail for performanceId: PERF_0, date: 2025-09-09
java.lang.RuntimeException: Test Exception
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.processMessage(MarketPriceEnrichedConsumer.java:58)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$0(MarketPriceEnrichedConsumer.java:39)
	at com.morningstar.martkafkaconsumer.util.LoggingContext.withTraceId(LoggingContext.java:31)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$1(MarketPriceEnrichedConsumer.java:39)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-09-12 19:31:22.488 [pool-3-thread-4] ERROR [9191e729-5014-4f28-bc64-c9ccaf52a341] c.m.m.c.MarketPriceEnrichedConsumer -- MarketPriceEnrichedConsumer: Failed to process MarketPriceDetail for performanceId: PERF_0, date: 2025-09-09
java.lang.RuntimeException: Test Exception
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.processMessage(MarketPriceEnrichedConsumer.java:58)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$0(MarketPriceEnrichedConsumer.java:39)
	at com.morningstar.martkafkaconsumer.util.LoggingContext.withTraceId(LoggingContext.java:31)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$1(MarketPriceEnrichedConsumer.java:39)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-09-12 19:31:22.488 [pool-3-thread-2] ERROR [9191e729-5014-4f28-bc64-c9ccaf52a341] c.m.m.c.MarketPriceEnrichedConsumer -- MarketPriceEnrichedConsumer: Failed to process MarketPriceDetail for performanceId: PERF_0, date: 2025-09-09
java.lang.RuntimeException: Test Exception
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.processMessage(MarketPriceEnrichedConsumer.java:58)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$0(MarketPriceEnrichedConsumer.java:39)
	at com.morningstar.martkafkaconsumer.util.LoggingContext.withTraceId(LoggingContext.java:31)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$1(MarketPriceEnrichedConsumer.java:39)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-09-12 19:31:22.488 [pool-3-thread-6] ERROR [9191e729-5014-4f28-bc64-c9ccaf52a341] c.m.m.c.MarketPriceEnrichedConsumer -- MarketPriceEnrichedConsumer: Failed to process MarketPriceDetail for performanceId: PERF_0, date: 2025-09-09
java.lang.RuntimeException: Test Exception
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.processMessage(MarketPriceEnrichedConsumer.java:58)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$0(MarketPriceEnrichedConsumer.java:39)
	at com.morningstar.martkafkaconsumer.util.LoggingContext.withTraceId(LoggingContext.java:31)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$1(MarketPriceEnrichedConsumer.java:39)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-09-12 19:31:22.489 [runnable-consumer-0] INFO  [9191e729-5014-4f28-bc64-c9ccaf52a341] c.m.m.c.MarketPriceEnrichedConsumer -- MarketPriceEnrichedConsumer: Completed processing 6 messages with pollId: 9191e729-5014-4f28-bc64-c9ccaf52a341 in 2031ms
2025-09-12 19:31:22.490 [runnable-consumer-0] INFO  [9191e729-5014-4f28-bc64-c9ccaf52a341] c.m.m.c.KafkaPluginConsumerEndpoint -- Committing topic: devcloud_market_price_enriched_event offsets: {0=45}
2025-09-12 19:31:22.492 [runnable-consumer-0] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- processAsyncCommit() is called with {devcloud_market_price_enriched_event-0=OffsetAndMetadata{offset=45, leaderEpoch=null, metadata=''}}
2025-09-12 19:31:22.582 [runnable-consumer-1] INFO  [] c.m.m.c.KafkaPluginConsumerEndpoint -- Committed topic: devcloud_market_price_enriched_event, partition: 0, offset: 45
2025-09-12 19:36:00.536 [runnable-consumer-0] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- Trying to close Kafka consumer, RunnableConsumer.isRunning: false
2025-09-12 19:36:07.425 [main] INFO  [] c.m.m.security.AuthInitializer -- [Authentication] Keytab: C:\Users\<USER>\AppData\Local\Temp\service-keytab-8284113957444838494.keytab
2025-09-12 19:36:07.428 [main] INFO  [] c.m.m.security.AuthInitializer -- [Authentication] JAAS conf: C:\Users\<USER>\AppData\Local\Temp\jaas-11889556686268234172.conf
2025-09-12 19:36:07.428 [main] INFO  [] c.m.m.security.AuthInitializer -- [Authentication] krb5 conf: C:\Users\<USER>\AppData\Local\Temp\krb5-6215771733149298034.conf
2025-09-12 19:36:07.432 [main] INFO  [] c.m.m.MartKafkaConsumerApplication -- Starting MartKafkaConsumerApplication using Java 17.0.9 with PID 34612 (C:\Zeng\dataac\msstash\mart-kafka-consumer\target\classes started by wzeng1 in C:\Zeng\dataac\msstash\mart-kafka-consumer)
2025-09-12 19:36:07.432 [main] DEBUG [] c.m.m.MartKafkaConsumerApplication -- Running with Spring Boot v3.5.5, Spring v6.2.10
2025-09-12 19:36:07.433 [main] INFO  [] c.m.m.MartKafkaConsumerApplication -- The following 1 profile is active: "dev"
2025-09-12 19:36:07.736 [main] INFO  [] o.s.d.r.c.RepositoryConfigurationDelegate -- Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-12 19:36:07.738 [main] INFO  [] o.s.d.r.c.RepositoryConfigurationDelegate -- Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-12 19:36:07.764 [main] INFO  [] o.s.d.r.c.RepositoryConfigurationDelegate -- Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces.
2025-09-12 19:36:07.928 [main] INFO  [] c.m.m.config.ExecutorConfig -- Created MarketPrice ExecutorService with 20 threads
2025-09-12 19:36:09.403 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Kafka factory fully initialized with region (aws-us-east-1) and env (devcloud)
2025-09-12 19:36:09.415 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- [DEPRECATED-Should not be here] - Done setting a system property for serde class (key:serde_pojo_class_name_tag, value:com.morningstar.data.domain.eod.events.MarketPriceEvent)
2025-09-12 19:36:09.417 [main] INFO  [] c.m.d.m.c.s.a.AvroNoSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.eod.events.MarketPriceEvent, codecName: null)
2025-09-12 19:36:09.650 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 19:36:09.652 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- This generic serde is using (serdePojoClassName: class com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 19:36:09.739 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- About to send an 'adminMessage' to this workflow (clients_connections_details) about this client connection: ProducerRecord(topic=devcloud_clients_connections_details, partition=null, headers=RecordHeaders(headers = [], isReadOnly = false), key=market_price_list_updated|DPDA_Market_Price_Consumer|CA-JT26LR3, value={"clientId": "DPDA_Market_Price_Consumer", "workflowId": "market_price_list_updated", "connectionDescriptor": {"region": "aws-us-east-1", "env": "devcloud", "hostname": "CA-JT26LR3", "port": -1, "pid": 34612, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757720169648", "endDateTime": null, "error": null}, timestamp=null)
2025-09-12 19:36:10.365 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Sent an 'adminMessage' to this workflow (clients_connections_details) about this client connection: ProducerRecord(topic=devcloud_clients_connections_details, partition=null, headers=RecordHeaders(headers = [], isReadOnly = true), key=market_price_list_updated|DPDA_Market_Price_Consumer|CA-JT26LR3, value={"clientId": "DPDA_Market_Price_Consumer", "workflowId": "market_price_list_updated", "connectionDescriptor": {"region": "aws-us-east-1", "env": "devcloud", "hostname": "CA-JT26LR3", "port": -1, "pid": 34612, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757720169648", "endDateTime": null, "error": null}, timestamp=null)
2025-09-12 19:36:10.719 [main] WARN  [] c.m.d.m.common.impl.KafkaFactory -- This method is being deprecated, we should not use it going forward! Please use other overloaded methods. Done building fully configured consumer (org.apache.kafka.clients.consumer.KafkaConsumer@2e67df05). 
Morningstar custom properties:
	{tasks_number_tag=1, serde_pojo_class_name_tag=com.morningstar.data.domain.eod.events.MarketPriceEvent, topic_name_tag=devcloud_market_price_list_updated}. 
This Consumer Connection Details:
	{"clientId": "DPDA_Market_Price_Consumer", "workflowId": "market_price_list_updated", "connectionDescriptor": {"region": "aws-us-east-1", "env": "devcloud", "hostname": "CA-JT26LR3", "port": -1, "pid": 34612, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757720169648", "endDateTime": null, "error": null}
2025-09-12 19:36:10.726 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- workflow market_price_list_updated enable.auto.commit is configured as false
2025-09-12 19:36:10.728 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- Done initialize this 'RunnableConsumer' - Details:
	Workflow: market_price_list_updated
	Overrides: [{enable.auto.commit=false, serde_pojo_class_name_tag=com.morningstar.data.domain.eod.events.MarketPriceEvent, group.id=dpda_market_price_group, auto.offset.reset=earliest, client.id=DPDA_Market_Price_Consumer}]
2025-09-12 19:36:10.729 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Done building fully configured consumer (com.morningstar.dp.messaging.common.impl.RunnableConsumer@330a6b76)
2025-09-12 19:36:10.730 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- [DEPRECATED-Should not be here] - Done setting a system property for serde class (key:serde_pojo_class_name_tag, value:com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent)
2025-09-12 19:36:10.730 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent, codecName: null)
2025-09-12 19:36:10.733 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- This generic serde is using (serdePojoClassName: class com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent, codecName: null)
2025-09-12 19:36:10.733 [main] WARN  [] c.m.d.m.common.impl.KafkaFactory -- client id DPDA_Market_Price_Consumer already exists, will change it to DPDA_Market_Price_Consumer-0
2025-09-12 19:36:10.740 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 19:36:10.740 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- This generic serde is using (serdePojoClassName: class com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 19:36:10.740 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- About to send an 'adminMessage' to this workflow (clients_connections_details) about this client connection: ProducerRecord(topic=devcloud_clients_connections_details, partition=null, headers=RecordHeaders(headers = [], isReadOnly = false), key=market_price_enriched_event|DPDA_Market_Price_Consumer-0|CA-JT26LR3, value={"clientId": "DPDA_Market_Price_Consumer-0", "workflowId": "market_price_enriched_event", "connectionDescriptor": {"region": "aws-us-east-1", "env": "devcloud", "hostname": "CA-JT26LR3", "port": -1, "pid": 34612, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757720170740", "endDateTime": null, "error": null}, timestamp=null)
2025-09-12 19:36:10.740 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- This is for auditing only so no issue to worry about. Will not produce into the kafka-Plugin admin topic (clients_connections_details) for now.
2025-09-12 19:36:10.741 [main] WARN  [] c.m.d.m.common.impl.KafkaFactory -- This method is being deprecated, we should not use it going forward! Please use other overloaded methods. Done building fully configured consumer (org.apache.kafka.clients.consumer.KafkaConsumer@7b4185ea). 
Morningstar custom properties:
	{tasks_number_tag=1, serde_pojo_class_name_tag=com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent, topic_name_tag=devcloud_market_price_enriched_event}. 
This Consumer Connection Details:
	{"clientId": "DPDA_Market_Price_Consumer-0", "workflowId": "market_price_enriched_event", "connectionDescriptor": {"region": "aws-us-east-1", "env": "devcloud", "hostname": "CA-JT26LR3", "port": -1, "pid": 34612, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757720170740", "endDateTime": null, "error": null}
2025-09-12 19:36:10.741 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- workflow market_price_enriched_event enable.auto.commit is configured as false
2025-09-12 19:36:10.741 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- Done initialize this 'RunnableConsumer' - Details:
	Workflow: market_price_enriched_event
	Overrides: [{enable.auto.commit=false, serde_pojo_class_name_tag=com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent, group.id=dpda_market_price_group, auto.offset.reset=earliest, client.id=DPDA_Market_Price_Consumer}]
2025-09-12 19:36:10.741 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Done building fully configured consumer (com.morningstar.dp.messaging.common.impl.RunnableConsumer@1fd2f96d)
2025-09-12 19:36:11.160 [main] INFO  [] c.m.m.MartKafkaConsumerApplication -- Started MartKafkaConsumerApplication in 5.092 seconds (process running for 5.596)
2025-09-12 19:36:11.170 [main] INFO  [] c.m.m.c.AbstractMessageConsumer -- Starting Kafka message consumer for workflow: market_price_enriched_event
2025-09-12 19:36:11.171 [main] INFO  [] c.m.m.c.AbstractMessageConsumer -- Kafka message consumer started successfully
2025-09-12 19:36:27.553 [runnable-consumer-0] INFO  [e368ed87-d461-4375-adfb-c17f03c8d2ab] c.m.m.c.KafkaPluginConsumerEndpoint -- topic: devcloud_market_price_enriched_event, record partition 0, offset: 45, key: null, pollId: e368ed87-d461-4375-adfb-c17f03c8d2ab, value: {"market_price_data_list": [{"performance_id": "PERF_0", "investment_type": "Stock", "date": 2025-09-09, "high": 100.00000000000000000, "low": 90.00000000000000000, "open": 95.00000000000000000, "close": 98.00000000000000000, "bid": null, "mid": null, "ask": null, "currency_code": "USD", "traded_volume": 1000000.00000000000000000, "copy_over_reason": "PRICE"}], "exchange_detail": null, "batch_metadata": null, "event_timestamp_utc": 2025-09-12T23:36:26.780Z}
2025-09-12 19:36:27.553 [runnable-consumer-0] INFO  [e368ed87-d461-4375-adfb-c17f03c8d2ab] c.m.m.c.AbstractMessageConsumer -- Received 1 messages with pollId: e368ed87-d461-4375-adfb-c17f03c8d2ab
2025-09-12 19:36:27.553 [runnable-consumer-0] INFO  [e368ed87-d461-4375-adfb-c17f03c8d2ab] c.m.m.c.MarketPriceEnrichedConsumer -- MarketPriceEnrichedConsumer Processing 1 messages with pollId: e368ed87-d461-4375-adfb-c17f03c8d2ab
2025-09-12 19:36:27.555 [pool-3-thread-1] INFO  [e368ed87-d461-4375-adfb-c17f03c8d2ab] c.m.m.service.MarketPriceDataService -- Storing market price data for investmentId: PERF_0, date: 2025-09-09
2025-09-12 19:36:29.548 [runnable-consumer-0] ERROR [e368ed87-d461-4375-adfb-c17f03c8d2ab] c.m.m.c.AbstractMessageConsumer -- Default error handling: failed to process 1 messages, pollId: e368ed87-d461-4375-adfb-c17f03c8d2ab, messages: [{"market_price_data_list": [{"performance_id": "PERF_0", "investment_type": "Stock", "date": 2025-09-09, "high": 100.00000000000000000, "low": 90.00000000000000000, "open": 95.00000000000000000, "close": 98.00000000000000000, "bid": null, "mid": null, "ask": null, "currency_code": "USD", "traded_volume": 1000000.00000000000000000, "copy_over_reason": "PRICE"}], "exchange_detail": null, "batch_metadata": null, "event_timestamp_utc": 2025-09-12T23:36:26.780Z}]
java.util.concurrent.CompletionException: java.lang.RuntimeException: Failed to store market price data
	at java.base/java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:315)
	at java.base/java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:320)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1807)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.lang.RuntimeException: Failed to store market price data
	at com.morningstar.martkafkaconsumer.service.MarketPriceDataService.storeMarketPriceDetail(MarketPriceDataService.java:44)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.processMessage(MarketPriceEnrichedConsumer.java:56)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$0(MarketPriceEnrichedConsumer.java:39)
	at com.morningstar.martkafkaconsumer.util.LoggingContext.withTraceId(LoggingContext.java:31)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$1(MarketPriceEnrichedConsumer.java:39)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	... 3 common frames omitted
2025-09-12 19:36:29.552 [runnable-consumer-0] ERROR [] c.m.d.m.common.impl.RunnableConsumer -- Got an exception while trying to notify a listener with new payload(s)
java.util.concurrent.CompletionException: java.lang.RuntimeException: Failed to store market price data
	at java.base/java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:315)
	at java.base/java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:320)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1807)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.lang.RuntimeException: Failed to store market price data
	at com.morningstar.martkafkaconsumer.service.MarketPriceDataService.storeMarketPriceDetail(MarketPriceDataService.java:44)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.processMessage(MarketPriceEnrichedConsumer.java:56)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$0(MarketPriceEnrichedConsumer.java:39)
	at com.morningstar.martkafkaconsumer.util.LoggingContext.withTraceId(LoggingContext.java:31)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$1(MarketPriceEnrichedConsumer.java:39)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	... 3 common frames omitted
2025-09-12 19:36:29.552 [runnable-consumer-0] ERROR [] c.m.m.c.KafkaPluginConsumerEndpoint -- Received IllegalStateException while consuming from kafka
java.util.concurrent.CompletionException: java.lang.RuntimeException: Failed to store market price data
	at java.base/java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:315)
	at java.base/java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:320)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1807)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.lang.RuntimeException: Failed to store market price data
	at com.morningstar.martkafkaconsumer.service.MarketPriceDataService.storeMarketPriceDetail(MarketPriceDataService.java:44)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.processMessage(MarketPriceEnrichedConsumer.java:56)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$0(MarketPriceEnrichedConsumer.java:39)
	at com.morningstar.martkafkaconsumer.util.LoggingContext.withTraceId(LoggingContext.java:31)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$1(MarketPriceEnrichedConsumer.java:39)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	... 3 common frames omitted
2025-09-12 19:37:26.968 [runnable-consumer-0] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- Trying to close Kafka consumer, RunnableConsumer.isRunning: false
2025-09-12 19:37:40.188 [main] INFO  [] c.m.m.security.AuthInitializer -- [Authentication] Keytab: C:\Users\<USER>\AppData\Local\Temp\service-keytab-8978212620236154103.keytab
2025-09-12 19:37:40.190 [main] INFO  [] c.m.m.security.AuthInitializer -- [Authentication] JAAS conf: C:\Users\<USER>\AppData\Local\Temp\jaas-5642100762985108208.conf
2025-09-12 19:37:40.190 [main] INFO  [] c.m.m.security.AuthInitializer -- [Authentication] krb5 conf: C:\Users\<USER>\AppData\Local\Temp\krb5-16375665698473806100.conf
2025-09-12 19:37:40.192 [main] INFO  [] c.m.m.MartKafkaConsumerApplication -- Starting MartKafkaConsumerApplication using Java 17.0.9 with PID 37500 (C:\Zeng\dataac\msstash\mart-kafka-consumer\target\classes started by wzeng1 in C:\Zeng\dataac\msstash\mart-kafka-consumer)
2025-09-12 19:37:40.193 [main] DEBUG [] c.m.m.MartKafkaConsumerApplication -- Running with Spring Boot v3.5.5, Spring v6.2.10
2025-09-12 19:37:40.193 [main] INFO  [] c.m.m.MartKafkaConsumerApplication -- The following 1 profile is active: "dev"
2025-09-12 19:37:40.469 [main] INFO  [] o.s.d.r.c.RepositoryConfigurationDelegate -- Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-12 19:37:40.471 [main] INFO  [] o.s.d.r.c.RepositoryConfigurationDelegate -- Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-12 19:37:40.494 [main] INFO  [] o.s.d.r.c.RepositoryConfigurationDelegate -- Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
2025-09-12 19:37:40.625 [main] INFO  [] c.m.m.config.ExecutorConfig -- Created MarketPrice ExecutorService with 20 threads
2025-09-12 19:37:41.942 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Kafka factory fully initialized with region (aws-us-east-1) and env (devcloud)
2025-09-12 19:37:41.955 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- [DEPRECATED-Should not be here] - Done setting a system property for serde class (key:serde_pojo_class_name_tag, value:com.morningstar.data.domain.eod.events.MarketPriceEvent)
2025-09-12 19:37:41.957 [main] INFO  [] c.m.d.m.c.s.a.AvroNoSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.eod.events.MarketPriceEvent, codecName: null)
2025-09-12 19:37:42.189 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 19:37:42.191 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- This generic serde is using (serdePojoClassName: class com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 19:37:42.275 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- About to send an 'adminMessage' to this workflow (clients_connections_details) about this client connection: ProducerRecord(topic=devcloud_clients_connections_details, partition=null, headers=RecordHeaders(headers = [], isReadOnly = false), key=market_price_list_updated|DPDA_Market_Price_Consumer|CA-JT26LR3, value={"clientId": "DPDA_Market_Price_Consumer", "workflowId": "market_price_list_updated", "connectionDescriptor": {"region": "aws-us-east-1", "env": "devcloud", "hostname": "CA-JT26LR3", "port": -1, "pid": 37500, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757720262187", "endDateTime": null, "error": null}, timestamp=null)
2025-09-12 19:37:42.854 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Sent an 'adminMessage' to this workflow (clients_connections_details) about this client connection: ProducerRecord(topic=devcloud_clients_connections_details, partition=null, headers=RecordHeaders(headers = [], isReadOnly = true), key=market_price_list_updated|DPDA_Market_Price_Consumer|CA-JT26LR3, value={"clientId": "DPDA_Market_Price_Consumer", "workflowId": "market_price_list_updated", "connectionDescriptor": {"region": "aws-us-east-1", "env": "devcloud", "hostname": "CA-JT26LR3", "port": -1, "pid": 37500, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757720262187", "endDateTime": null, "error": null}, timestamp=null)
2025-09-12 19:37:43.211 [main] WARN  [] c.m.d.m.common.impl.KafkaFactory -- This method is being deprecated, we should not use it going forward! Please use other overloaded methods. Done building fully configured consumer (org.apache.kafka.clients.consumer.KafkaConsumer@41861964). 
Morningstar custom properties:
	{tasks_number_tag=1, serde_pojo_class_name_tag=com.morningstar.data.domain.eod.events.MarketPriceEvent, topic_name_tag=devcloud_market_price_list_updated}. 
This Consumer Connection Details:
	{"clientId": "DPDA_Market_Price_Consumer", "workflowId": "market_price_list_updated", "connectionDescriptor": {"region": "aws-us-east-1", "env": "devcloud", "hostname": "CA-JT26LR3", "port": -1, "pid": 37500, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757720262187", "endDateTime": null, "error": null}
2025-09-12 19:37:43.221 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- workflow market_price_list_updated enable.auto.commit is configured as false
2025-09-12 19:37:43.222 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- Done initialize this 'RunnableConsumer' - Details:
	Workflow: market_price_list_updated
	Overrides: [{enable.auto.commit=false, serde_pojo_class_name_tag=com.morningstar.data.domain.eod.events.MarketPriceEvent, group.id=dpda_market_price_group, auto.offset.reset=earliest, client.id=DPDA_Market_Price_Consumer}]
2025-09-12 19:37:43.223 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Done building fully configured consumer (com.morningstar.dp.messaging.common.impl.RunnableConsumer@7a6433f3)
2025-09-12 19:37:43.225 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- [DEPRECATED-Should not be here] - Done setting a system property for serde class (key:serde_pojo_class_name_tag, value:com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent)
2025-09-12 19:37:43.225 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent, codecName: null)
2025-09-12 19:37:43.229 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- This generic serde is using (serdePojoClassName: class com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent, codecName: null)
2025-09-12 19:37:43.229 [main] WARN  [] c.m.d.m.common.impl.KafkaFactory -- client id DPDA_Market_Price_Consumer already exists, will change it to DPDA_Market_Price_Consumer-0
2025-09-12 19:37:43.235 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- About to initialize this generic serde with (serdePojoClassName: com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 19:37:43.235 [main] INFO  [] c.m.d.m.c.s.a.AvroWithSchemaGenericSerde -- This generic serde is using (serdePojoClassName: class com.morningstar.data.domain.common.connection.ConnectedClientDetails, codecName: null)
2025-09-12 19:37:43.235 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- About to send an 'adminMessage' to this workflow (clients_connections_details) about this client connection: ProducerRecord(topic=devcloud_clients_connections_details, partition=null, headers=RecordHeaders(headers = [], isReadOnly = false), key=market_price_enriched_event|DPDA_Market_Price_Consumer-0|CA-JT26LR3, value={"clientId": "DPDA_Market_Price_Consumer-0", "workflowId": "market_price_enriched_event", "connectionDescriptor": {"region": "aws-us-east-1", "env": "devcloud", "hostname": "CA-JT26LR3", "port": -1, "pid": 37500, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757720263235", "endDateTime": null, "error": null}, timestamp=null)
2025-09-12 19:37:43.235 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- This is for auditing only so no issue to worry about. Will not produce into the kafka-Plugin admin topic (clients_connections_details) for now.
2025-09-12 19:37:43.236 [main] WARN  [] c.m.d.m.common.impl.KafkaFactory -- This method is being deprecated, we should not use it going forward! Please use other overloaded methods. Done building fully configured consumer (org.apache.kafka.clients.consumer.KafkaConsumer@3b07b706). 
Morningstar custom properties:
	{tasks_number_tag=1, serde_pojo_class_name_tag=com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent, topic_name_tag=devcloud_market_price_enriched_event}. 
This Consumer Connection Details:
	{"clientId": "DPDA_Market_Price_Consumer-0", "workflowId": "market_price_enriched_event", "connectionDescriptor": {"region": "aws-us-east-1", "env": "devcloud", "hostname": "CA-JT26LR3", "port": -1, "pid": 37500, "thisLibraryVersion": {"name": "kafka-plugin-1.0.12-RELEASE.jar", "version": "kafka-plugin-1.0.12-RELEASE.jar", "libType": "INTERNAL"}, "dependentLibraryVersion": [{"name": "kafka-clients-3.9.1.jar", "version": "kafka-clients-3.9.1.jar", "libType": "EXTERNAL"}, {"name": "domain-objects-1.0.143-20250903.212337-4.jar", "version": "domain-objects-1.0.143-20250903.212337-4.jar", "libType": "INTERNAL"}], "connectedClientType": "CONSUMER"}, "startDateTime": "1757720263235", "endDateTime": null, "error": null}
2025-09-12 19:37:43.236 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- workflow market_price_enriched_event enable.auto.commit is configured as false
2025-09-12 19:37:43.236 [main] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- Done initialize this 'RunnableConsumer' - Details:
	Workflow: market_price_enriched_event
	Overrides: [{enable.auto.commit=false, serde_pojo_class_name_tag=com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent, group.id=dpda_market_price_group, auto.offset.reset=earliest, client.id=DPDA_Market_Price_Consumer}]
2025-09-12 19:37:43.236 [main] INFO  [] c.m.d.m.common.impl.KafkaFactory -- Done building fully configured consumer (com.morningstar.dp.messaging.common.impl.RunnableConsumer@7b4185ea)
2025-09-12 19:37:43.610 [main] INFO  [] c.m.m.MartKafkaConsumerApplication -- Started MartKafkaConsumerApplication in 4.851 seconds (process running for 5.413)
2025-09-12 19:37:43.612 [main] INFO  [] c.m.m.c.AbstractMessageConsumer -- Starting Kafka message consumer for workflow: market_price_enriched_event
2025-09-12 19:37:43.613 [main] INFO  [] c.m.m.c.AbstractMessageConsumer -- Kafka message consumer started successfully
2025-09-12 19:37:47.887 [runnable-consumer-0] INFO  [38df063e-950c-4bb4-a18d-d1674b101100] c.m.m.c.KafkaPluginConsumerEndpoint -- topic: devcloud_market_price_enriched_event, record partition 0, offset: 45, key: null, pollId: 38df063e-950c-4bb4-a18d-d1674b101100, value: {"market_price_data_list": [{"performance_id": "PERF_0", "investment_type": "Stock", "date": 2025-09-09, "high": 100.00000000000000000, "low": 90.00000000000000000, "open": 95.00000000000000000, "close": 98.00000000000000000, "bid": null, "mid": null, "ask": null, "currency_code": "USD", "traded_volume": 1000000.00000000000000000, "copy_over_reason": "PRICE"}], "exchange_detail": null, "batch_metadata": null, "event_timestamp_utc": 2025-09-12T23:36:26.780Z}
2025-09-12 19:37:47.888 [runnable-consumer-0] INFO  [38df063e-950c-4bb4-a18d-d1674b101100] c.m.m.c.KafkaPluginConsumerEndpoint -- topic: devcloud_market_price_enriched_event, record partition 0, offset: 46, key: null, pollId: 38df063e-950c-4bb4-a18d-d1674b101100, value: {"market_price_data_list": [{"performance_id": "PERF_0", "investment_type": "Stock", "date": 2025-09-09, "high": 100.00000000000000000, "low": 90.00000000000000000, "open": 95.00000000000000000, "close": 98.00000000000000000, "bid": null, "mid": null, "ask": null, "currency_code": "USD", "traded_volume": 1000000.00000000000000000, "copy_over_reason": "PRICE"}], "exchange_detail": null, "batch_metadata": null, "event_timestamp_utc": 2025-09-12T23:37:39.963Z}
2025-09-12 19:37:47.888 [runnable-consumer-0] INFO  [38df063e-950c-4bb4-a18d-d1674b101100] c.m.m.c.AbstractMessageConsumer -- Received 2 messages with pollId: 38df063e-950c-4bb4-a18d-d1674b101100
2025-09-12 19:37:47.888 [runnable-consumer-0] INFO  [38df063e-950c-4bb4-a18d-d1674b101100] c.m.m.c.MarketPriceEnrichedConsumer -- MarketPriceEnrichedConsumer Processing 2 messages with pollId: 38df063e-950c-4bb4-a18d-d1674b101100
2025-09-12 19:37:47.889 [pool-3-thread-1] INFO  [38df063e-950c-4bb4-a18d-d1674b101100] c.m.m.service.MarketPriceDataService -- Storing market price data for investmentId: PERF_0, date: 2025-09-09
2025-09-12 19:37:47.889 [pool-3-thread-2] INFO  [38df063e-950c-4bb4-a18d-d1674b101100] c.m.m.service.MarketPriceDataService -- Storing market price data for investmentId: PERF_0, date: 2025-09-09
2025-09-12 19:37:49.908 [runnable-consumer-0] ERROR [38df063e-950c-4bb4-a18d-d1674b101100] c.m.m.c.AbstractMessageConsumer -- Default error handling: failed to process 2 messages, pollId: 38df063e-950c-4bb4-a18d-d1674b101100, messages: [{"market_price_data_list": [{"performance_id": "PERF_0", "investment_type": "Stock", "date": 2025-09-09, "high": 100.00000000000000000, "low": 90.00000000000000000, "open": 95.00000000000000000, "close": 98.00000000000000000, "bid": null, "mid": null, "ask": null, "currency_code": "USD", "traded_volume": 1000000.00000000000000000, "copy_over_reason": "PRICE"}], "exchange_detail": null, "batch_metadata": null, "event_timestamp_utc": 2025-09-12T23:36:26.780Z}, {"market_price_data_list": [{"performance_id": "PERF_0", "investment_type": "Stock", "date": 2025-09-09, "high": 100.00000000000000000, "low": 90.00000000000000000, "open": 95.00000000000000000, "close": 98.00000000000000000, "bid": null, "mid": null, "ask": null, "currency_code": "USD", "traded_volume": 1000000.00000000000000000, "copy_over_reason": "PRICE"}], "exchange_detail": null, "batch_metadata": null, "event_timestamp_utc": 2025-09-12T23:37:39.963Z}]
java.util.concurrent.CompletionException: java.lang.RuntimeException: Failed to store market price data
	at java.base/java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:315)
	at java.base/java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:320)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1807)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.lang.RuntimeException: Failed to store market price data
	at com.morningstar.martkafkaconsumer.service.MarketPriceDataService.storeMarketPriceDetail(MarketPriceDataService.java:44)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.processMessage(MarketPriceEnrichedConsumer.java:56)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$0(MarketPriceEnrichedConsumer.java:39)
	at com.morningstar.martkafkaconsumer.util.LoggingContext.withTraceId(LoggingContext.java:31)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$1(MarketPriceEnrichedConsumer.java:39)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	... 3 common frames omitted
2025-09-12 19:37:49.912 [runnable-consumer-0] ERROR [] c.m.d.m.common.impl.RunnableConsumer -- Got an exception while trying to notify a listener with new payload(s)
java.util.concurrent.CompletionException: java.lang.RuntimeException: Failed to store market price data
	at java.base/java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:315)
	at java.base/java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:320)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1807)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.lang.RuntimeException: Failed to store market price data
	at com.morningstar.martkafkaconsumer.service.MarketPriceDataService.storeMarketPriceDetail(MarketPriceDataService.java:44)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.processMessage(MarketPriceEnrichedConsumer.java:56)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$0(MarketPriceEnrichedConsumer.java:39)
	at com.morningstar.martkafkaconsumer.util.LoggingContext.withTraceId(LoggingContext.java:31)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$1(MarketPriceEnrichedConsumer.java:39)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	... 3 common frames omitted
2025-09-12 19:37:49.913 [runnable-consumer-0] ERROR [] c.m.m.c.KafkaPluginConsumerEndpoint -- Received IllegalStateException while consuming from kafka
java.util.concurrent.CompletionException: java.lang.RuntimeException: Failed to store market price data
	at java.base/java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:315)
	at java.base/java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:320)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1807)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.lang.RuntimeException: Failed to store market price data
	at com.morningstar.martkafkaconsumer.service.MarketPriceDataService.storeMarketPriceDetail(MarketPriceDataService.java:44)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.processMessage(MarketPriceEnrichedConsumer.java:56)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$0(MarketPriceEnrichedConsumer.java:39)
	at com.morningstar.martkafkaconsumer.util.LoggingContext.withTraceId(LoggingContext.java:31)
	at com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer.lambda$processMessages$1(MarketPriceEnrichedConsumer.java:39)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	... 3 common frames omitted
2025-09-12 19:43:43.544 [runnable-consumer-0] INFO  [] c.m.d.m.common.impl.RunnableConsumer -- Trying to close Kafka consumer, RunnableConsumer.isRunning: false
